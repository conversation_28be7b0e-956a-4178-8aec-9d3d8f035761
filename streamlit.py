import streamlit as st
import requests
import json
import time
from datetime import datetime
import pandas as pd

# Configuración de la página
st.set_page_config(
    page_title="🔍 Batuta AI",
    page_icon="🛒",
    layout="wide",
    initial_sidebar_state="expanded"
)

# CSS personalizado para hacer la app más bonita
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea, #764ba2);
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        text-align: center;
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .prediction-card {
        background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 5px solid #667eea;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
    }
    
    .result-box {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        border: 2px solid #e1e5e9;
        margin: 0.5rem 0;
        text-align: center;
    }
    
    .category-result {
        background: linear-gradient(135deg, #84fab0, #8fd3f4);
        color: #2c3e50;
        font-weight: bold;
        font-size: 1.2rem;
    }
    
    .comercial-result {
        background: linear-gradient(135deg, #ffecd2, #fcb69f);
        color: #2c3e50;
        font-weight: bold;
        font-size: 1.2rem;
    }
    
    .example-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 3px solid #28a745;
        margin: 0.5rem 0;
    }
    
    .stButton > button {
        background: linear-gradient(90deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.5rem 2rem;
        font-size: 1.1rem;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
    
    .error-box {
        background: #fee;
        border: 1px solid #fcc;
        border-radius: 8px;
        padding: 1rem;
        color: #c33;
    }
    
    .success-box {
        background: #efe;
        border: 1px solid #cfc;
        border-radius: 8px;
        padding: 1rem;
        color: #363;
    }
    
    /* Estilos para botones de feedback */
    .feedback-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        border: 1px solid #dee2e6;
    }
    
    .correction-form {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    
    /* Estilos para ejemplos clickeables */
    .example-button {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        border: 1px solid #90caf9;
        border-radius: 8px;
        padding: 0.8rem;
        margin: 0.3rem 0;
        cursor: pointer;
        transition: all 0.3s;
        text-align: left;
    }
    
    .example-button:hover {
        background: linear-gradient(135deg, #bbdefb, #90caf9);
        transform: translateX(5px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* Mejorar apariencia de métricas */
    .metric-container {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)

# Configuración de la API
API_BASE_URL = "http://localhost:5000"

# Header principal
st.markdown("""
<div class="main-header">
    <h1>🔍 Software Classifier - Batuta AI</h1>
    <p>Clasifica software de inventarios.</p>
</div>
""", unsafe_allow_html=True)

# Sidebar con información
with st.sidebar:
    st.markdown("## 📋 Información")
    st.markdown("""
    **🤖 Modelos utilizados:**
    - Logistic Regression
    - LightGBM
    - XGBoost  
    - Random Forest
    - Ensemble
    
    **🎯 Sistema de Voting:**
    La predicción final es la más votada entre los 5 modelos.
    """)
    
    st.markdown("---")
    
    # Estado de la API
    st.markdown("## 🔌 Estado de la API")
    try:
        response = requests.get(f"{API_BASE_URL}/classifier/health", timeout=3)
        if response.status_code == 200:
            st.success("✅ API Conectada")
            health_data = response.json()
            st.text(f"⏰ {health_data.get('timestamp', 'N/A')[:19]}")
        else:
            st.error("❌ API No Responde")
    except:
        st.error("❌ API No Disponible")
        st.warning("Asegúrate de que la API esté ejecutándose en localhost:5000")

# Función para hacer predicciones
def predict_product(name, vendor):
    """Hacer predicción a la API"""
    try:
        payload = {"name": name, "vendor": vendor}
        response = requests.post(
            f"{API_BASE_URL}/classifier/predict",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            return response.json(), None
        else:
            return None, f"Error {response.status_code}: {response.text}"
            
    except requests.exceptions.ConnectionError:
        return None, "❌ No se puede conectar a la API. Verifica que esté ejecutándose."
    except requests.exceptions.Timeout:
        return None, "⏰ Timeout: La API tardó demasiado en responder."
    except Exception as e:
        return None, f"❌ Error inesperado: {str(e)}"

# Layout principal en columnas
col1, col2 = st.columns([2, 1])

with col1:
    st.markdown("### 📝 Ingresa los datos del producto")
    
    # Inicializar session_state si no existe
    if 'form_name' not in st.session_state:
        st.session_state.form_name = ''
    if 'form_vendor' not in st.session_state:
        st.session_state.form_vendor = ''
    
    # Formulario de entrada
    with st.form("prediction_form"):
        # Campos de entrada usando session_state
        name = st.text_input(
            "🏷️ Nombre del Producto",
            value=st.session_state.form_name,
            placeholder="Ej: CrowdStrike Device Control",
            help="Ingresa el nombre del producto a clasificar"
        )
        
        vendor = st.text_input(
            "🏢 Vendor/Marca",
            value=st.session_state.form_vendor,
            placeholder="Ej: CrowdStrike, Inc.",
            help="Ingresa la marca o fabricante del producto"
        )
        
        # Botón de predicción
        submitted = st.form_submit_button("🚀 Clasificar Producto")
        
        # Limpiar los valores del formulario después del submit
        if submitted:
            st.session_state.form_name = ''
            st.session_state.form_vendor = ''
    
    # Procesar el submit FUERA del formulario
    if submitted:
        # Limpiar resultados anteriores
        if 'current_result' in st.session_state:
            st.session_state.current_result = None
        if 'show_correction_form' in st.session_state:
            st.session_state.show_correction_form = False
            
        if not name or not vendor:
            st.error("❌ Por favor completa ambos campos")
        else:
            # Mostrar indicador de carga
            with st.spinner("🤖 Analizando producto con 5 modelos ML..."):
                result, error = predict_product(name, vendor)
            
            if error:
                st.markdown(f'<div class="error-box">{error}</div>', unsafe_allow_html=True)
            else:
                # Guardar resultado en session_state para los botones de feedback
                st.session_state.current_result = {
                    'name': name,
                    'vendor': vendor,
                    'category': result['category'],
                    'comercial_name': result['comercial_name'],
                    'timestamp': result.get('timestamp', 'N/A')
                }
                
                # Mostrar resultados
                st.markdown("### 🎯 Resultados de la Clasificación")
                
                # Crear columnas para los resultados
                res_col1, res_col2 = st.columns(2)
                
                with res_col1:
                    st.markdown(f"""
                    <div class="result-box category-result">
                        <h4>📂 Categoría</h4>
                        <p>{result['category']}</p>
                    </div>
                    """, unsafe_allow_html=True)
                
                with res_col2:
                    st.markdown(f"""
                    <div class="result-box comercial-result">
                        <h4>🏷️ Nombre Comercial</h4>
                        <p>{result['comercial_name']}</p>
                    </div>
                    """, unsafe_allow_html=True)
                
                # Información adicional
                st.markdown(f"""
                <div class="success-box">
                    <strong>✅ Clasificación completada exitosamente</strong><br>
                    <small>⏰ Procesado el {result.get('timestamp', 'N/A')[:19]}</small>
                </div>
                """, unsafe_allow_html=True)

# Sistema de feedback FUERA del formulario
if 'current_result' in st.session_state and st.session_state.current_result:
    st.markdown("---")
    st.markdown("### 📊 ¿Es correcta la clasificación?")
    
    result = st.session_state.current_result
    
    feedback_col1, feedback_col2, feedback_col3 = st.columns([1, 1, 2])
    
    with feedback_col1:
        if st.button("👍 Correcto", key="thumbs_up", type="primary"):
            st.success("¡Gracias por tu feedback positivo!")
            # Guardar feedback positivo en historial
            feedback_data = {
                'name': result['name'],
                'vendor': result['vendor'],
                'category': result['category'],
                'comercial_name': result['comercial_name'],
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'feedback': 'positive'
            }
            if 'history' not in st.session_state:
                st.session_state.history = []
            st.session_state.history.append(feedback_data)
            # Limpiar resultado actual
            st.session_state.current_result = None
            st.rerun()
    
    with feedback_col2:
        if st.button("👎 Incorrecto", key="thumbs_down", type="secondary"):
            st.session_state.show_correction_form = True
    
    # Formulario de corrección (aparece cuando se presiona thumbs down)
    if st.session_state.get('show_correction_form', False):
        st.markdown("---")
        st.markdown("### ✏️ Corrección Manual")
        st.info("Por favor ingresa la clasificación correcta:")
        
        with st.form("correction_form"):
            correct_category = st.text_input(
                "📂 Categoría Correcta", 
                placeholder=f"Predicción actual: {result['category']}"
            )
            correct_comercial = st.text_input(
                "🏷️ Nombre Comercial Correcto", 
                placeholder=f"Predicción actual: {result['comercial_name']}"
            )
            
            col_submit, col_cancel = st.columns(2)
            with col_submit:
                submit_correction = st.form_submit_button("💾 Guardar Corrección", type="primary")
            with col_cancel:
                cancel_correction = st.form_submit_button("❌ Cancelar")
        
        # Procesar corrección FUERA del formulario
        if submit_correction:
            if correct_category and correct_comercial:
                # Guardar corrección en historial
                correction_data = {
                    'name': result['name'],
                    'vendor': result['vendor'],
                    'category': correct_category,
                    'comercial_name': correct_comercial,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'feedback': 'corrected',
                    'original_category': result['category'],
                    'original_comercial': result['comercial_name']
                }
                if 'history' not in st.session_state:
                    st.session_state.history = []
                st.session_state.history.append(correction_data)
                
                st.success("✅ Corrección guardada. ¡Gracias por ayudar a mejorar el modelo!")
                st.session_state.show_correction_form = False
                st.session_state.current_result = None
                time.sleep(1)
                st.rerun()
            else:
                st.error("❌ Por favor completa ambos campos de corrección")
        
        if cancel_correction:
            st.session_state.show_correction_form = False
            st.rerun()

with col2:
    st.markdown("### 💡 Ejemplos de Productos")
    
    # Mostrar indicador si hay valores pre-cargados
    if st.session_state.form_name or st.session_state.form_vendor:
        st.success(f"✅ Ejemplo seleccionado: {st.session_state.form_name[:30]}...")
    
    # Ejemplos reales basados en los datos proporcionados
    examples = [
        {"name": "Image Control", "vendor": "ADD Systems", "category": "Multimedia Software"},
        {"name": "CrowdStrike Device Control", "vendor": "CrowdStrike, Inc.", "category": "EDR"},
        {"name": "Browser for SQL Server 2022", "vendor": "Microsoft Corporation", "category": "Database"},
        {"name": "Google Chrome", "vendor": "Google LLC", "category": "Web Browser"},
        {"name": "VLC media player", "vendor": "VideoLAN", "category": "Multimedia Software"},
        {"name": "TeamViewer 13 Host", "vendor": "TeamViewer", "category": "Communication"},
        {"name": "Skype versión 8.127", "vendor": "Skype Technologies S.A.", "category": "Business Software"},
        {"name": "Cisco AnyConnect Secure Mobility Client", "vendor": "Cisco Systems, Inc.", "category": "Security Software"}
    ]
    
    for i, example in enumerate(examples):
        # Destacar el ejemplo seleccionado
        is_selected = (st.session_state.form_name == example['name'])
        button_type = "primary" if is_selected else "secondary"
        
        if st.button(f"📋 {example['name'][:25]}{'...' if len(example['name']) > 25 else ''}", 
                    key=f"example_{i}", 
                    help=f"Vendor: {example['vendor']}\nCategoría esperada: {example['category']}",
                    use_container_width=True,
                    type=button_type):
            # Actualizar los valores en session_state
            st.session_state.form_name = example['name']
            st.session_state.form_vendor = example['vendor']
            st.rerun()
    
    # Botón para limpiar selección
    if st.session_state.form_name or st.session_state.form_vendor:
        if st.button("🗑️ Limpiar Campos", use_container_width=True):
            st.session_state.form_name = ''
            st.session_state.form_vendor = ''
            st.rerun()

# Sección de historial
if 'history' in st.session_state and st.session_state.history:
    st.markdown("---")
    st.markdown("### 📊 Historial de Clasificaciones")
    
    # Crear DataFrame del historial
    df_history = pd.DataFrame(st.session_state.history)
    
    # Procesar datos para mostrar mejor
    if not df_history.empty:
        # Agregar íconos de feedback
        df_display = df_history.copy()
        df_display['Feedback'] = df_display['feedback'].map({
            'positive': '👍 Correcto',
            'corrected': '✏️ Corregido',
            'pending': '⏳ Pendiente'
        })
        
        # Reordenar columnas
        columns_order = ['name', 'vendor', 'category', 'comercial_name', 'Feedback', 'timestamp']
        df_display = df_display[columns_order]
        df_display.columns = ['Nombre', 'Vendor', 'Categoría', 'Nombre Comercial', 'Estado', 'Fecha']
        
        # Mostrar tabla con formato
        st.dataframe(
            df_display,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Estado": st.column_config.TextColumn(
                    width="small"
                ),
                "Fecha": st.column_config.DatetimeColumn(
                    width="medium"
                )
            }
        )
        
        # Botones de acción para el historial
        hist_col1, hist_col2, hist_col3 = st.columns(3)
        
        with hist_col1:
            if st.button("🗑️ Limpiar Historial"):
                st.session_state.history = []
                st.rerun()
        
        with hist_col2:
            if st.button("📥 Descargar CSV"):
                csv = df_display.to_csv(index=False)
                st.download_button(
                    label="💾 Descargar",
                    data=csv,
                    file_name=f"clasificaciones_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
        
        with hist_col3:
            if st.button("📈 Ver Correcciones"):
                corrected_items = df_history[df_history['feedback'] == 'corrected']
                if not corrected_items.empty:
                    st.subheader("✏️ Elementos Corregidos")
                    for _, item in corrected_items.iterrows():
                        with st.expander(f"📝 {item['name']} - {item['vendor']}"):
                            col1, col2 = st.columns(2)
                            with col1:
                                st.markdown("**Predicción Original:**")
                                st.write(f"📂 {item.get('original_category', 'N/A')}")
                                st.write(f"🏷️ {item.get('original_comercial', 'N/A')}")
                            with col2:
                                st.markdown("**Corrección:**")
                                st.write(f"📂 {item['category']}")
                                st.write(f"🏷️ {item['comercial_name']}")
                else:
                    st.info("No hay correcciones registradas aún.")
        
        # Estadísticas mejoradas
        st.markdown("---")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("📈 Total Clasificados", len(df_history))
        
        with col2:
            categories_count = df_history['category'].nunique()
            st.metric("📂 Categorías Únicas", categories_count)
        
        with col3:
            positive_feedback = len(df_history[df_history['feedback'] == 'positive'])
            accuracy_rate = f"{(positive_feedback/len(df_history)*100):.1f}%" if len(df_history) > 0 else "0%"
            st.metric("👍 Feedback Positivo", accuracy_rate)
        
        with col4:
            corrections_count = len(df_history[df_history['feedback'] == 'corrected'])
            st.metric("✏️ Correcciones", corrections_count)
        
        # Gráfico de feedback (si hay suficientes datos)
        if len(df_history) >= 3:
            st.markdown("### 📊 Distribución de Feedback")
            feedback_counts = df_history['feedback'].value_counts()
            feedback_labels = {
                'positive': '👍 Correcto',
                'corrected': '✏️ Corregido',
                'pending': '⏳ Pendiente'
            }
            
            chart_data = pd.DataFrame({
                'Estado': [feedback_labels.get(k, k) for k in feedback_counts.index],
                'Cantidad': feedback_counts.values
            })
            
            st.bar_chart(chart_data.set_index('Estado'))

# Footer
st.markdown("---")
st.markdown("""
<div style="text-align: center; color: #666; padding: 1rem;">
    <p>🤖 Powered by Batuta | 🚀 Built with mucho huevo</p>
    <small>Clasificador de productos usando ensemble de 5 modelos ML</small>
</div>
""", unsafe_allow_html=True)