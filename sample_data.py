import json
import random
import math

def sample_json_percentage(input_file, output_file, percentage=10):
    # Leer el archivo JSON
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Calcular el tamaño del sample basado en el porcentaje
    total_records = len(data)
    sample_size = math.ceil(total_records * percentage / 100)
    
    print(f"Total de registros en el dataset: {total_records}")
    print(f"Tamaño del sample ({percentage}%): {sample_size} registros")
    
    # Tomar una muestra aleatoria sin reemplazo
    sampled_data = random.sample(data, sample_size)
    
    # Guardar el resultado en un nuevo archivo JSON
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(sampled_data, f, ensure_ascii=False, indent=2)
    
    print(f"Sample del {percentage}% guardado exitosamente en {output_file}")

if __name__ == "__main__":
    input_file = "software_clasiffier/synthetic_inventory_dataset.json"  # Reemplaza con la ruta de tu archivo JSON
    output_file = "software_clasiffier/sample_synthetic_inventory_dataset.json"  # Nombre del archivo de salida
    percentage = 10  # Porcentaje para el sample
    
    sample_json_percentage(input_file, output_file, percentage)