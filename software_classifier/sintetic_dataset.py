from typing import Dict, Any, List, Optional
import os
import json
import logging
import time
import langchain
from datetime import datetime
from langchain_openai import ChatOpenAI  # Cambiado de AzureChatOpenAI a ChatOpenAI
from langchain.schema import SystemMessage
from pydantic import BaseModel, Field
from langchain_core.output_parsers import JsonOutputParser
from langchain_community.cache import InMemoryCache
from dotenv import load_dotenv
from langchain.prompts import ChatPromptTemplate, HumanMessagePromptTemplate
from tqdm import tqdm

# Load environment variables
load_dotenv()

# Configure LangChain debug mode
langchain.debug = False

# Activar caché global para LangChain
langchain.llm_cache = InMemoryCache()

# Define the software inventory item model
class SoftwareInventoryItem(BaseModel):
    Category: str = Field(description="Category of the software")
    ComercialName: str = Field(description="Commercial name of the software")
    Vendor: str = Field(description="Vendor or manufacturer of the software")
    Name: str = Field(description="Name of the software")

class SoftwareClassifier:
    """
    SoftwareClassifier class to classify software inventory items using a language model.
    
    Args:
        logger: Logger instance for logging activities.
        categories (List[str]): List of valid software categories.
        batch_size (int): Number of items to process in a single batch.
    """
    def __init__(self, logger, batch_size: int = 2, model_name: str = "gpt-3.5-turbo"):
        self.logger = logger
        self.parser = JsonOutputParser(pydantic_object=SoftwareInventoryItem)
        self.batch_size = batch_size
        self.model_name = model_name
    
        self.system_prompt = f"""You are an expert in software classification specialized in categorizing software applications.

        Below is the list of categories, each accompanied by a brief description of its primary scope. When classifying, use only one of these exact names and follow the description to decide.

        Categories = [
            "AI Tools":                 "Software that provides artificial intelligence capabilities (models, assistants, predictive analytics).",
            "Antivirus":                "Programs designed to detect, prevent, and remove malware (viruses, trojans, etc.).",
            "Backup":                   "Solutions for data backup and restoration.",
            "Business Software":        "Applications focused on business processes (ERP, CRM, accounting).",
            "Development Tools":        "Tools for developers (IDEs, compilers, libraries, SDKs).",
            "EDR":                      "Endpoint Detection and Response: systems for detecting and responding to threats on endpoints.",
            "Multimedia":               "Software for playback, editing, or creation of audio, video, or images and Gaming.",
            "Ofimatica":                "Office suite software: word processors, spreadsheets, presentation tools.",
            "Operating System":         "Operating systems for workstations or servers.",
            "Remote Access":            "Tools that allow connecting to or controlling another computer remotely.",
            "Security Software":        "General security applications (desktop firewalls, disk encryption, DLP).",
            "Utility":                  "General-purpose utility tools (disk cleaners, compression utilities, system monitors, Cloud Storage, Communication, Database, Driver, GIS).",
            "Virtualization":           "Applications that create virtual environments (hypervisors, containers).",
            "VPN":                      "Virtual Private Network solutions to secure connections to remote networks.",
            "Vulnerability Management": "Systems for scanning and managing vulnerabilities in infrastructures.",
            "Web Browser":              "Internet browsers (Chrome, Firefox, Edge, Safari).",
            "Unknown":                  "When it is not possible to determine an appropriate category.",
            "Secure Software Edge":     "A set of cloud-based perimeter security services (SWG, CASB, ZTNA)."
        ]

        Analyze the software information and format the data according to these rules. Respond only in JSON.

        If processing multiple items, respond with a JSON array of objects, one for each input.

        For each item, provide:
        {{
            "Category":      "Select the most appropriate category from the list above. If you are not sure, choose \"Unknown\".",
            "ComercialName": "A concise commercial name (omit version numbers or builds), the name commonly used to refer to this software.",
            "Vendor":        "The vendor exactly as provided in the input.",
            "Name":          "The name exactly as provided in the input."
        }}

        For the “Category” field:
        - Use only one of the exact names from the list (case-sensitive).
        - Refer to the description to guide your choice.

        For the “ComercialName” field:
        - Extract or generate a brief commercial name that reflects the product without versions.
        - Example: “Microsoft Visual C++ 2022 X64 Additional Runtime - 14.38.33135” → “Visual C++ Runtime”.
        - Do not include version numbers or build identifiers unless they are part of the brand identity.
        - Identify the product family (e.g., “CrowdStrike Falcon” for “CrowdStrike Sensor Platform”).
        - Look at similar software in existing ground truth to guide your naming decisions.

        """
        
        # Create chat prompt template for single item classification
        self.single_prompt_template = ChatPromptTemplate.from_messages([
            SystemMessage(content=self.system_prompt),
            HumanMessagePromptTemplate.from_template("Classify this software: Vendor: {vendor}, Name: {name}")
        ])
        
        # Create chat prompt template for batch classification
        self.batch_prompt_template = ChatPromptTemplate.from_messages([
            SystemMessage(content=self.system_prompt),
            HumanMessagePromptTemplate.from_template("Classify these software items: {items}")
        ])
        
        # Initialize the OpenAI model with API key from environment variables
        try:
            self.model = ChatOpenAI(
                model_name=self.model_name,  # Usar el modelo especificado en la inicialización
                openai_api_key=os.getenv("OPENAI_API_KEY"),
                temperature=0.5,  # Lower temperature for more deterministic results
                streaming=False,  # Disable streaming for batch processing
                request_timeout=60.0,  # Aumentamos el timeout para batch processing
                cache=True,  # Activar caché explícitamente
            )
        except Exception as e:
            self.logger.error(f"Failed to initialize OpenAI: {str(e)}")
            raise
        
        # Create a chain that passes prompts through the model - for single items
        self.single_chain = self.single_prompt_template | self.model
        
        # Create a chain for batch processing
        self.batch_chain = self.batch_prompt_template | self.model
    
    def repair_json(self, json_str: str) -> str:
        """
        Intenta reparar JSON incompleto o malformado.
        
        Args:
            json_str (str): La cadena JSON potencialmente incompleta.
            
        Returns:
            str: El JSON reparado o la cadena original si no se puede reparar.
        """
        # Si comienza con markdown code block, intenta extraer solo el JSON
        if json_str.strip().startswith('```'):
            # Extract JSON from markdown code block
            lines = json_str.strip().split('\n')
            # Remove the opening code block
            if lines[0].startswith('```'):
                lines = lines[1:]
            # Try to find closing code block and remove it
            if '```' in lines:
                end_index = lines.index('```')
                lines = lines[:end_index]
            json_str = '\n'.join(lines)
        
        # Si comienza con un corchete o llave pero no termina correctamente
        if json_str.strip().startswith('[') and not json_str.strip().endswith(']'):
            # Intenta completar el JSON
            json_str = json_str.strip() + ']'
        
        if json_str.strip().startswith('{') and not json_str.strip().endswith('}'):
            # Intenta completar el JSON
            json_str = json_str.strip() + '}'
        
        # Verifica si el JSON es válido
        try:
            json.loads(json_str)
            return json_str
        except json.JSONDecodeError:
            # Si aún falla, intenta extraer objetos JSON individuales
            try:
                if json_str.strip().startswith('['):
                    # Busca objetos JSON completos dentro de la cadena
                    objects = []
                    obj_start = None
                    brace_count = 0
                    in_string = False
                    escape_next = False
                    
                    for i, char in enumerate(json_str):
                        if char == '\\' and not escape_next:
                            escape_next = True
                            continue
                        
                        if char == '"' and not escape_next:
                            in_string = not in_string
                        
                        if not in_string:
                            if char == '{':
                                if brace_count == 0:
                                    obj_start = i
                                brace_count += 1
                            elif char == '}':
                                brace_count -= 1
                                if brace_count == 0 and obj_start is not None:
                                    obj_json = json_str[obj_start:i+1]
                                    try:
                                        obj = json.loads(obj_json)
                                        objects.append(obj)
                                    except json.JSONDecodeError:
                                        pass
                                    obj_start = None
                        
                        escape_next = False
                    
                    if objects:
                        return json.dumps(objects)
            except Exception as e:
                self.logger.error(f"Error while trying to repair JSON: {e}")
            
            return json_str
    
    def classify_software(self, vendor: str, name: str, retry_count: int = 0) -> Dict[str, Any]:
        """
        Classify a single software item using the provided vendor and name information.

        Args:
            vendor (str): The software vendor.
            name (str): The software name.
            retry_count (int): The current retry attempt number.

        Returns:
            Dict[str, Any]: The classified software information.
        """
        timestamp = datetime.now().strftime('%Y-%m-%d_%H:%M:%S')
        
        # Log input
        self.logger.info(f"{timestamp}_Classifying: Vendor: {vendor}, Name: {name}")
            
        try:
            # Backoff for retries to avoid rate limits
            if retry_count > 0:
                sleep_time = retry_count * 2  # Progressive backoff
                self.logger.info(f"Backoff sleeping for {sleep_time} seconds on retry {retry_count}")
                time.sleep(sleep_time)
                
            # Invoke the chat prompt chain to generate a classification
            response = self.single_chain.invoke({
                "vendor": vendor,
                "name": name
            })

            # Log the output
            self.logger.info(f"{timestamp}_Response: {response.content}")

            try:
                # Intentar reparar el JSON si es necesario
                json_str = self.repair_json(response.content)
                
                # Parse the response
                parsed_response = self.parser.parse(json_str)
                self.logger.info(f"Parsed response: {parsed_response}")
                return parsed_response
            except Exception as parse_error:
                # Log parsing errors
                self.logger.warning(f"Parsing failed: {parse_error}")
                # If parsing fails, return the original item with empty category and commercial name
                return {
                    "Category": "",
                    "ComercialName": "",
                    "Vendor": vendor,
                    "Name": name
                }

        except Exception as e:
            # Log any errors that occurred during response generation
            self.logger.error(f"Error generating classification: {e}")
            # Return the original item with empty category and commercial name
            return {
                "Category": "",
                "ComercialName": "",
                "Vendor": vendor,
                "Name": name
            }
    
    def classify_batch(self, items: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        Classify a batch of software items in a single API call.

        Args:
            items (List[Dict[str, str]]): List of items with vendor and name.

        Returns:
            List[Dict[str, Any]]: List of classified software items.
        """
        if not items:
            return []
        
        # Format items as a text string for the prompt
        items_text = "\n".join([f"Item {i+1}: Vendor: {item.get('Vendor', '')}, Name: {item.get('Name', '')}" 
                            for i, item in enumerate(items)])
        
        timestamp = datetime.now().strftime('%Y-%m-%d_%H:%M:%S')
        self.logger.info(f"{timestamp}_Classifying batch of {len(items)} items")
        
        try:
            # Invoke the batch prompt chain
            response = self.batch_chain.invoke({"items": items_text})
            
            # Log complete response for debugging
            self.logger.debug(f"{timestamp}_Complete batch response: {response.content}")
            
            # Log shortened version of response
            response_preview = response.content[:200] + "..." if len(response.content) > 200 else response.content
            self.logger.info(f"{timestamp}_Batch response preview: {response_preview}")
            
            # Intentar reparar el JSON si es necesario
            json_str = self.repair_json(response.content)
            
            # Check if response is empty after repair
            if not json_str or json_str.strip() == '':
                self.logger.error(f"Empty JSON response received after repair")
                raise ValueError("Empty JSON response received from the model")
            
            try:
                # Parse JSON array from response
                parsed_data = json.loads(json_str)
                
                # Ensure we get a list of dictionaries
                if isinstance(parsed_data, dict):
                    parsed_data = [parsed_data]  # Convert single dict to list
                
                # Ensure the response matches our expected fields
                result = []
                for i, item in enumerate(items):
                    if i < len(parsed_data) and isinstance(parsed_data[i], dict) and "Category" in parsed_data[i] and "ComercialName" in parsed_data[i]:
                        # Use parsed result with original vendor/name for safety
                        result.append({
                            "Category": parsed_data[i].get("Category", ""),
                            "ComercialName": parsed_data[i].get("ComercialName", ""),
                            "Vendor": item.get("Vendor", ""),  # Use original vendor
                            "Name": item.get("Name", "")  # Use original name
                        })
                    else:
                        # If parsing failed for this item, include empty result
                        self.logger.warning(f"Parsing failed or incomplete data for item {i} in batch")
                        result.append({
                            "Category": "",
                            "ComercialName": "",
                            "Vendor": item.get("Vendor", ""),
                            "Name": item.get("Name", "")
                        })
                
                # If number of results doesn't match items, pad with empty results
                while len(result) < len(items):
                    i = len(result)
                    result.append({
                        "Category": "",
                        "ComercialName": "",
                        "Vendor": items[i].get("Vendor", ""),
                        "Name": items[i].get("Name", "")
                    })
                
                return result
                
            except json.JSONDecodeError as json_error:
                self.logger.error(f"JSON parsing error in batch: {json_error} - Raw content: {json_str[:500]}")
                raise
            except Exception as parse_error:
                self.logger.error(f"Error parsing batch response: {parse_error}")
                raise
        
        except Exception as e:
            self.logger.error(f"Error in batch classification: {e}")
            self.logger.info("Falling back to individual processing for batch...")
        
        # If anything fails, process items individually as fallback
        self.logger.warning("Processing items individually as fallback...")
        results = []
        for item in items:
            retries = 0
            max_individual_retries = 3
            while retries < max_individual_retries:
                try:
                    result = self.classify_software(item.get("Vendor", ""), item.get("Name", ""), retries)
                    results.append(result)
                    break
                except Exception as e:
                    retries += 1
                    self.logger.error(f"Error processing individual item (try {retries}/{max_individual_retries}): {e}")
                    if retries >= max_individual_retries:
                        self.logger.error(f"Failed to process item after {max_individual_retries} retries. Adding empty result.")
                        results.append({
                            "Category": "",
                            "ComercialName": "",
                            "Vendor": item.get("Vendor", ""),
                            "Name": item.get("Name", "")
                        })
        return results

def process_inventory(input_file: str, output_file: str, logger, batch_size: int = 2, model_name: str = "gpt-3.5-turbo", max_retries: int = 3):
    """
    Process the inventory file and create a synthetic dataset with classified software.
    Utiliza procesamiento por lotes y guardado incremental para mayor eficiencia.

    Args:
        input_file (str): Path to the input JSON inventory file.
        output_file (str): Path to the output file for the synthetic dataset.
        categories (List[str]): List of valid software categories.
        logger: Logger instance for logging activities.
        batch_size (int): Number of items to process in each batch.
        model_name (str): Nombre del modelo de OpenAI a utilizar (por defecto: "gpt-3.5-turbo").
        max_retries (int): Número máximo de reintentos para procesar un lote en caso de error.
    """
    # Initialize the classifier with the specified batch size and model
    classifier = SoftwareClassifier(logger, batch_size, model_name)
    
    # Load the inventory data
    with open(input_file, 'r') as f:
        inventory_data = json.load(f)
    
    # Check if output file already exists (for resuming)
    classified_inventory = []
    if os.path.exists(output_file):
        try:
            with open(output_file, 'r') as f:
                classified_inventory = json.load(f)
                logger.info(f"Loaded {len(classified_inventory)} existing records from {output_file}")
                print(f"Cargando {len(classified_inventory)} registros existentes desde {output_file}")
        except Exception as e:
            logger.warning(f"Error loading existing output file: {e}. Starting from scratch.")
            classified_inventory = []
    
    # Calculate how many items are left to process
    processed_count = len(classified_inventory)
    remaining_items = inventory_data[processed_count:]
    total_remaining = len(remaining_items)
    total_items = len(inventory_data)
    
    if processed_count > 0:
        print(f"\nContinuando desde el ítem {processed_count+1}/{total_items} - Faltan {total_remaining} elementos")
    else:
        print(f"\nIniciando clasificación de {total_items} elementos de software...\n")
    
    # Contador para guardado incremental
    save_counter = 0
    
    # Create batches from remaining items
    batches = []
    for i in range(0, len(remaining_items), batch_size):
        batches.append(remaining_items[i:i + batch_size])
    
    # Create a progress bar with tqdm
    with tqdm(total=total_remaining, initial=0, desc="Clasificando software", 
              bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]') as pbar:
        
        for batch in batches:
            retry_count = 0
            batch_processed = False
            
            # Intentar procesar el lote con reintentos en caso de error
            while not batch_processed and retry_count < max_retries:
                try:
                    # Classify batch
                    results = classifier.classify_batch(batch)
                    
                    # Add results to classified inventory
                    classified_inventory.extend(results)
                    
                    # Update progress bar
                    pbar.update(len(batch))
                    
                    # Update save counter
                    save_counter += len(batch)
                    
                    # Mark batch as processed successfully
                    batch_processed = True
                    
                except Exception as e:
                    retry_count += 1
                    logger.error(f"Error processing batch (attempt {retry_count}/{max_retries}): {e}")
                    print(f"\nError al procesar lote. Reintentando ({retry_count}/{max_retries})...")
                    
                    # Si hay demasiados errores, reducir el tamaño del lote
                    if retry_count >= 2 and len(batch) > 1:
                        # Dividir el lote en dos y procesarlos individualmente
                        mid = len(batch) // 2
                        first_half = batch[:mid]
                        second_half = batch[mid:]
                        
                        logger.info(f"Dividiendo lote de {len(batch)} elementos en 2 lotes de {len(first_half)} y {len(second_half)} elementos")
                        print(f"Dividiendo lote para un procesamiento más estable...")
                        
                        try:
                            # Procesar primera mitad
                            results1 = classifier.classify_batch(first_half)
                            classified_inventory.extend(results1)
                            
                            # Procesar segunda mitad
                            results2 = classifier.classify_batch(second_half)
                            classified_inventory.extend(results2)
                            
                            # Update progress bar
                            pbar.update(len(batch))
                            
                            # Update save counter
                            save_counter += len(batch)
                            
                            # Mark batch as processed successfully
                            batch_processed = True
                        except Exception as e2:
                            logger.error(f"Error al procesar lote dividido: {e2}")
                            # En última instancia, procesar uno por uno
                            if retry_count == max_retries - 1:
                                logger.warning("Procesando elementos individualmente como último recurso")
                                for item in batch:
                                    try:
                                        result = classifier.classify_software(item.get("Vendor", ""), item.get("Name", ""))
                                        classified_inventory.append(result)
                                        pbar.update(1)
                                        save_counter += 1
                                    except Exception as e3:
                                        logger.error(f"Error al procesar elemento individual: {e3}")
                                        # Agregar un elemento vacío si todo falla
                                        classified_inventory.append({
                                            "Category": "",
                                            "ComercialName": "",
                                            "Vendor": item.get("Vendor", ""),
                                            "Name": item.get("Name", "")
                                        })
                                        pbar.update(1)
                                        save_counter += 1
                                
                                # Mark batch as processed
                                batch_processed = True
            
            # Si después de todos los reintentos no se pudo procesar, agregar elementos vacíos
            if not batch_processed:
                logger.error(f"No se pudo procesar un lote después de {max_retries} intentos. Agregando elementos vacíos.")
                print(f"\nError persistente. Continuando con siguiente lote...")
                
                for item in batch:
                    classified_inventory.append({
                        "Category": "",
                        "ComercialName": "",
                        "Vendor": item.get("Vendor", ""),
                        "Name": item.get("Name", "")
                    })
                
                # Update progress bar
                pbar.update(len(batch))
                
                # Update save counter
                save_counter += len(batch)
            
            # Save progress every ~100 items
            if save_counter >= 100:
                with open(output_file, 'w') as f:
                    json.dump(classified_inventory, indent=2, fp=f)
                logger.info(f"Guardado parcial: {len(classified_inventory)}/{total_items} items procesados")
                print(f"\nGuardado parcial completado: {len(classified_inventory)}/{total_items} elementos")
                save_counter = 0  # Reset counter
    
    # Final save
    with open(output_file, 'w') as f:
        json.dump(classified_inventory, indent=2, fp=f)
    
    logger.info(f"Processed {len(classified_inventory)} items. Synthetic dataset saved to {output_file}")
    print(f"\n\nProcesamiento completado! Se han clasificado {len(classified_inventory)} elementos.")
    print(f"Dataset sintético guardado en: {output_file}")
    return classified_inventory


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.ERROR,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("software_classification.log"),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger("SoftwareClassifier")
    
    
    # Verificar que la API key está configurada
    if not os.getenv("OPENAI_API_KEY"):
        print("ERROR: La API key de OpenAI no está configurada en la variable de entorno OPENAI_API_KEY")
        print("Configura esta variable con tu clave de API antes de ejecutar el script.")
        exit(1)
    
    # Configuración de procesamiento
    input_file = "inventory_clean_sample.json"
    output_file = "synthetic_inventory_dataset_new_category.json"
    batch_size = 10  # Reducido al mínimo para mayor estabilidad
    model_name = "gpt-4o-mini"  # Opciones: "gpt-3.5-turbo", "gpt-4", etc.
    max_retries = 3  # Número de reintentos para lotes fallidos
    
    print(f"\nUsando modelo OpenAI: {model_name}")
    print(f"Tamaño de lote: {batch_size}, Reintentos máximos: {max_retries}")
    classified_data = process_inventory(input_file, output_file, logger, batch_size, model_name, max_retries)
    logger.info(f"Classification complete. Generated {len(classified_data)} classified items.")
