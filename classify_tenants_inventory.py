#!/usr/bin/env python3
"""
Script para clasificar todo el inventario de software de todos los tenants
utilizando el endpoint batch del clasificador.
"""

import os
import json
import requests
import time
from pathlib import Path
from typing import List, Dict, Any
from collections import Counter

# Configuración
API_BASE_URL = "http://localhost:5000"
BATCH_ENDPOINT = f"{API_BASE_URL}/classifier/predict/batch"
TENANTS_DIR = "front/data/tenant"
OUTPUT_FILE = "classification_results.json"
BATCH_SIZE = 50  # Procesar en lotes para evitar timeouts

def load_tenant_data(tenant_file_path: str) -> List[Dict[str, Any]]:
    """
    Carga los datos de un archivo de tenant
    
    Args:
        tenant_file_path: Ruta al archivo JSON del tenant
        
    Returns:
        Lista de hosts con sus aplicaciones
    """
    try:
        with open(tenant_file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error cargando {tenant_file_path}: {str(e)}")
        return []

def extract_applications_from_tenant(tenant_data: List[Dict], tenant_name: str) -> List[Dict[str, str]]:
    """
    Extrae todas las aplicaciones de un tenant
    
    Args:
        tenant_data: Datos del tenant (lista de hosts)
        tenant_name: Nombre del tenant
        
    Returns:
        Lista de aplicaciones con metadatos adicionales
    """
    applications = []
    
    for host in tenant_data:
        host_id = host.get('_source', {}).get('hostId', 'unknown')
        apps = host.get('_source', {}).get('Applications', [])
        
        for app in apps:
            # Manejar campos que pueden ser None
            name = app.get('Name')
            vendor = app.get('Vendor')
            
            # Convertir None a string vacío y luego hacer strip
            name = (name or '').strip()
            vendor = (vendor or '').strip()
            
            if name and vendor:  # Solo incluir apps con name y vendor no vacíos
                applications.append({
                    'name': name,
                    'vendor': vendor,
                    'tenant': tenant_name,
                    'host_id': host_id,
                    'original_app': app  # Mantener datos originales para referencia
                })
    
    return applications

def classify_applications_batch(applications: List[Dict[str, str]]) -> List[Dict[str, Any]]:
    """
    Clasifica una lista de aplicaciones usando el endpoint batch
    
    Args:
        applications: Lista de aplicaciones a clasificar
        
    Returns:
        Lista de aplicaciones con sus clasificaciones
    """
    # Preparar el payload para el endpoint batch
    items = [{'name': app['name'], 'vendor': app['vendor']} for app in applications]
    payload = {'items': items}
    
    try:
        print(f"🔄 Clasificando lote de {len(items)} aplicaciones...")
        response = requests.post(BATCH_ENDPOINT, json=payload, timeout=300)
        response.raise_for_status()
        
        predictions = response.json().get('predictions', [])
        
        # Combinar aplicaciones originales con predicciones
        results = []
        for app, prediction in zip(applications, predictions):
            results.append({
                'tenant': app['tenant'],
                'host_id': app['host_id'],
                'name': app['name'],
                'vendor': app['vendor'],
                'category': prediction.get('category', 'Error'),
                'comercial_name': prediction.get('comercial_name', 'Error'),
                'original_app': app['original_app']
            })
        
        return results
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error en la petición: {str(e)}")
        return []
    except Exception as e:
        print(f"❌ Error inesperado: {str(e)}")
        return []

def process_in_batches(applications: List[Dict[str, str]], batch_size: int = BATCH_SIZE) -> List[Dict[str, Any]]:
    """
    Procesa las aplicaciones en lotes para evitar timeouts
    
    Args:
        applications: Lista de todas las aplicaciones
        batch_size: Tamaño del lote
        
    Returns:
        Lista de todas las clasificaciones
    """
    all_results = []
    total_batches = (len(applications) + batch_size - 1) // batch_size
    
    for i in range(0, len(applications), batch_size):
        batch_num = (i // batch_size) + 1
        batch = applications[i:i + batch_size]
        
        print(f"📦 Procesando lote {batch_num}/{total_batches} ({len(batch)} aplicaciones)")
        
        results = classify_applications_batch(batch)
        if results:
            all_results.extend(results)
            print(f"✅ Lote {batch_num} completado")
        else:
            print(f"❌ Lote {batch_num} falló")
        
        # Pequeña pausa entre lotes para no saturar el servidor
        if i + batch_size < len(applications):
            time.sleep(1)
    
    return all_results

def save_results(results: List[Dict[str, Any]], output_file: str):
    """
    Guarda los resultados en un archivo JSON
    
    Args:
        results: Lista de resultados de clasificación
        output_file: Archivo donde guardar los resultados
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"💾 Resultados guardados en {output_file}")
    except Exception as e:
        print(f"❌ Error guardando resultados: {str(e)}")

def generate_summary(results: List[Dict[str, Any]]):
    """
    Genera un resumen de los resultados
    
    Args:
        results: Lista de resultados de clasificación
    """
    if not results:
        print("❌ No hay resultados para mostrar")
        return
    
    # Estadísticas básicas
    total_apps = len(results)
    tenants = set(r['tenant'] for r in results)
    categories = set(r['category'] for r in results if r['category'] != 'Error')
    comercial_names = set(r['comercial_name'] for r in results if r['comercial_name'] != 'Error')
    errors = sum(1 for r in results if r['category'] == 'Error' or r['comercial_name'] == 'Error')
    
    print("\n" + "="*60)
    print("📊 RESUMEN DE CLASIFICACIÓN")
    print("="*60)
    print(f"Total de aplicaciones procesadas: {total_apps}")
    print(f"Número de tenants: {len(tenants)}")
    print(f"Clasificaciones exitosas: {total_apps - errors}")
    print(f"Errores en clasificación: {errors}")
    print(f"Categorías únicas encontradas: {len(categories)}")
    print(f"Nombres comerciales únicos: {len(comercial_names)}")
    
    # Top 10 categorías más comunes
    category_counts = Counter(r['category'] for r in results if r['category'] != 'Error')
    print("\n🏆 Top 10 Categorías más comunes:")
    for category, count in category_counts.most_common(10):
        print(f"  {category}: {count}")
    
    # Top 10 nombres comerciales más comunes
    comercial_counts = Counter(r['comercial_name'] for r in results if r['comercial_name'] != 'Error')
    print("\n🏆 Top 10 Nombres Comerciales más comunes:")
    for comercial, count in comercial_counts.most_common(10):
        print(f"  {comercial}: {count}")

def main():
    """Función principal del script"""
    print("🚀 Iniciando clasificación de inventario de todos los tenants...")
    
    # Verificar que existe el directorio de tenants
    tenants_path = Path(TENANTS_DIR)
    if not tenants_path.exists():
        print(f"❌ El directorio {TENANTS_DIR} no existe")
        return
    
    # Verificar que la API está disponible
    try:
        health_response = requests.get(f"{API_BASE_URL}/classifier/health", timeout=10)
        health_response.raise_for_status()
        print("✅ API del clasificador está disponible")
    except Exception as e:
        print(f"❌ No se puede conectar a la API: {str(e)}")
        print(f"   Asegúrate de que el servidor esté corriendo en {API_BASE_URL}")
        return
    
    # Obtener todos los archivos de tenants
    tenant_files = list(tenants_path.glob("*_export.json"))
    print(f"📁 Encontrados {len(tenant_files)} archivos de tenants")
    
    all_applications = []
    
    # Procesar cada archivo de tenant
    for tenant_file in tenant_files:
        tenant_name = tenant_file.stem.replace('_export', '')
        print(f"📂 Procesando tenant: {tenant_name}")
        
        tenant_data = load_tenant_data(tenant_file)
        if tenant_data:
            apps = extract_applications_from_tenant(tenant_data, tenant_name)
            all_applications.extend(apps)
            print(f"   ✅ {len(apps)} aplicaciones encontradas")
        else:
            print(f"   ⚠️  No se encontraron aplicaciones o error al cargar")
    
    if not all_applications:
        print("❌ No se encontraron aplicaciones para clasificar")
        return
    
    print(f"\n📊 Total de aplicaciones a clasificar: {len(all_applications)}")
    
    # Clasificar todas las aplicaciones en lotes
    print("\n🔄 Iniciando clasificación...")
    results = process_in_batches(all_applications)
    
    if results:
        # Guardar resultados
        save_results(results, OUTPUT_FILE)
        
        # Mostrar resumen
        generate_summary(results)
        
        print(f"\n🎉 Clasificación completada! Ver resultados en {OUTPUT_FILE}")
    else:
        print("❌ No se pudieron obtener resultados de clasificación")

if __name__ == "__main__":
    main()
