import json
import os
import re
import unicodedata
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional, Union
from sentence_transformers import SentenceTransformer
from sklearn.linear_model import LogisticRegression
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import LabelEncoder
import lightgbm as lgb
import openai
import time
import logging
import warnings
import joblib
from tqdm import tqdm

# Suprimir advertencias verbosas
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

def serialize_for_json(obj):
    """
    Convierte valores NumPy a tipos Python nativos para permitir serialización JSON.
    """
    if isinstance(obj, (np.integer, np.int32, np.int64)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float32, np.float64)):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: serialize_for_json(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [serialize_for_json(item) for item in obj]
    else:
        return obj

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("classifier.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SoftwareClassifier:
    """
    Clasificador de software en tres niveles:
    1. Logistic Regression con embeddings de texto
    2. LightGBM con embeddings + TF-IDF como fallback
    3. LLM (OpenAI) como fallback final
    """
    
    def __init__(
        self,
        collection_name: str = "software_inventory",
        persist_directory: str = "./models",
        embedding_model: str = "all-MiniLM-L6-v2",  # Modelo más liviano, se puede cambiar a "all-MPNet-base-v2" para mayor precisión
        primary_threshold: float = 0.8,
        secondary_threshold: float = 0.6,
        openai_api_key: Optional[str] = None
    ):
        """
        Inicializa el clasificador de software.
        
        Args:
            collection_name: Nombre de la colección
            persist_directory: Directorio para guardar modelos
            embedding_model: Modelo de sentence-transformer a utilizar
            primary_threshold: Umbral para el clasificador primario (LogisticRegression)
            secondary_threshold: Umbral para el clasificador secundario (LightGBM)
            openai_api_key: API key para OpenAI (opcional)
        """
        self.collection_name = collection_name
        self.persist_directory = persist_directory
        self.embedding_model_name = embedding_model
        self.primary_threshold = primary_threshold
        self.secondary_threshold = secondary_threshold
        
        # Crear directorios si no existen
        os.makedirs(self.persist_directory, exist_ok=True)
        
        # Inicializar modelos
        logger.info(f"Cargando modelo de embeddings {embedding_model}...")
        print(f"Descargando/cargando modelo de embeddings {embedding_model}... (puede tardar en la primera ejecucion)")
        self.embedding_model = SentenceTransformer(embedding_model)
        print(f"Modelo de embeddings cargado correctamente")
        
        # Inicializar clasificadores
        self.category_logistic_model = None
        self.commercial_name_logistic_model = None
        self.category_lgbm_model = None
        self.commercial_name_lgbm_model = None
        
        # Label encoders
        self.category_encoder = LabelEncoder()
        self.commercial_name_encoder = LabelEncoder()
        
        # TF-IDF vectorizer
        self.tfidf_vectorizer = TfidfVectorizer(
            analyzer='word',
            ngram_range=(1, 3),
            max_features=5000,
            min_df=2
        )
        
        # Inicializar OpenAI si se proporciona una API key
        if openai_api_key:
            openai.api_key = openai_api_key
            self.use_llm = True
        else:
            self.use_llm = False
        
        self.data = []
        self.stats_data = {
            "collection_name": collection_name,
            "embedding_model": embedding_model,
            "total_items": 0
        }
        
        logger.info("Clasificador inicializado correctamente")
    
    def _preprocess_text(self, vendor: str, name: str) -> str:
        """
        Preprocesa el texto concatenando vendor y name, y limpiando el resultado.
        
        Args:
            vendor: Nombre del proveedor
            name: Nombre del software
            
        Returns:
            Texto limpio y concatenado
        """
        # Concatenar vendor y name
        if vendor and name:
            text = f"{vendor} - {name}"
        elif name:
            text = name
        elif vendor:
            text = vendor
        else:
            text = ""
        
        # Convertir a minúsculas
        text = text.lower()
        
        # Eliminar acentos
        text = unicodedata.normalize('NFKD', text).encode('ASCII', 'ignore').decode('utf-8')
        
        # Eliminar caracteres especiales excepto letras, números y espacios
        text = re.sub(r'[^a-z0-9\s]', ' ', text)
        
        # Eliminar espacios múltiples
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _get_embedding(self, text: str) -> np.ndarray:
        """
        Obtiene el embedding de un texto utilizando el modelo de sentence-transformer.
        
        Args:
            text: Texto a convertir en embedding
            
        Returns:
            Vector de embedding
        """
        if not text:
            # Si el texto está vacío, devolver un vector de ceros
            return np.zeros(self.embedding_model.get_sentence_embedding_dimension())
        
        return self.embedding_model.encode(text)
    
    def ingest_from_file(self, file_path: str, batch_size: int = 100, resume: bool = True) -> None:
        """
        Ingesta datos desde un archivo JSON y entrena los modelos.
        
        Args:
            file_path: Ruta al archivo JSON con datos de entrenamiento
            batch_size: Tamaño del lote para procesamiento
            resume: Si se debe intentar reanudar un entrenamiento previo
        """
        # Crear directorio de checkpoints si no existe
        checkpoint_dir = os.path.join(self.persist_directory, "checkpoints")
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Archivos de checkpoint
        checkpoint_data_file = os.path.join(checkpoint_dir, "training_data.pkl")
        checkpoint_meta_file = os.path.join(checkpoint_dir, "meta.json")
        checkpoint_embeddings_file = os.path.join(checkpoint_dir, "embeddings.npy")
        
        # Variable para controlar si reanudar entrenamiento o comenzar desde cero
        resume_training = False
        checkpoint_meta = {}
        embeddings = None
        texts = []
        categories = []
        commercial_names = []
        tfidf_features = None
        
        # Normalizar ruta de archivo para comparaciones
        file_path_normalized = os.path.normpath(file_path)
        
        # Verificar si hay un entrenamiento previo a reanudar
        if resume and os.path.exists(checkpoint_meta_file):
            try:
                # Cargar metadatos del checkpoint
                print(f"\nCheckpoint encontrado en: {checkpoint_meta_file}")
                with open(checkpoint_meta_file, 'r', encoding='utf-8') as f:
                    checkpoint_meta = json.load(f)
                
                # Imprimir detalles del checkpoint
                print("Contenido del checkpoint:")
                print(f"- Archivo: {checkpoint_meta.get('file_path')}")
                print(f"- Estado: {checkpoint_meta.get('status', 'desconocido')}")
                print(f"- Timestamp: {checkpoint_meta.get('timestamp')}")
                
                # Verificar que el checkpoint corresponde al mismo archivo de entrenamiento
                checkpoint_file_path = checkpoint_meta.get("file_path", "")
                print(f"Comparando rutas:\n- Actual: {file_path_normalized}\n- Checkpoint: {checkpoint_file_path}")
                
                if os.path.normpath(checkpoint_file_path) == file_path_normalized:
                    print(f"\nEncontrado entrenamiento previo para {file_path}")
                    print(f"Estado anterior: {checkpoint_meta.get('status', 'desconocido')}")
                    
                    # Verificar en qué etapa se quedó el entrenamiento
                    status = checkpoint_meta.get("status", "")
                    valid_states = ["embeddings_generated", "category_model_trained", "commercial_name_model_trained", 
                                  "lgbm_partial", "category_lgbm_trained", "completed"]
                    
                    # Comprobar si el estado es válido para reanudar
                    can_resume = status in valid_states or any(status.startswith(prefix) for prefix in ["category_model_trained", "commercial_name_model_trained", "lgbm_partial", "category_lgbm_partial"])
                    
                    if can_resume:
                        print(f"\nReanudando entrenamiento desde estado: {status}")
                        
                        # Intentar cargar datos preprocesados
                        if os.path.exists(checkpoint_data_file):
                            print("Cargando datos preprocesados...")
                            import pickle
                            try:
                                with open(checkpoint_data_file, 'rb') as f:
                                    data_dict = pickle.load(f)
                                    
                                # Extraer variables
                                self.data = data_dict.get("data", [])
                                texts = data_dict.get("texts", [])
                                categories = data_dict.get("categories", [])
                                commercial_names = data_dict.get("commercial_names", [])
                                tfidf_features = data_dict.get("tfidf_features", None)
                                
                                # Cargar label encoders
                                self.category_encoder = data_dict.get("category_encoder")
                                self.commercial_name_encoder = data_dict.get("commercial_name_encoder")
                                
                                # Cargar vectorizador TF-IDF
                                self.tfidf_vectorizer = data_dict.get("tfidf_vectorizer")
                                
                                # Actualizar estadísticas
                                self.stats_data["total_items"] = len(self.data)
                                print(f"Datos cargados: {len(self.data)} elementos")
                                
                                # Intentar cargar embeddings
                                if os.path.exists(checkpoint_embeddings_file):
                                    print("Cargando embeddings...")
                                    try:
                                        embeddings = np.load(checkpoint_embeddings_file)
                                        print(f"Embeddings cargados: {embeddings.shape}")
                                        resume_training = True
                                        
                                        # Cargar modelos si ya estaban entrenados
                                        if status.startswith("category_model_trained") or status.startswith("commercial_name_model_trained") or status.startswith("lgbm_partial") or status.startswith("category_lgbm_trained"):
                                            lgbm_model_path = os.path.join(checkpoint_dir, "category_lgbm_model.pkl")
                                            logistic_cat_path = os.path.join(checkpoint_dir, "category_logistic_model.pkl")
                                            logistic_com_path = os.path.join(checkpoint_dir, "commercial_name_logistic_model.pkl")
                                            
                                            # Cargar modelos LogisticRegression
                                            if os.path.exists(logistic_cat_path):
                                                print("Cargando modelo LogisticRegression para Category...")
                                                try:
                                                    self.category_logistic_model = joblib.load(logistic_cat_path)
                                                    print("Modelo cargado correctamente")
                                                except Exception as e:
                                                    print(f"Error al cargar modelo: {str(e)}")
                                            
                                            if os.path.exists(logistic_com_path):
                                                print("Cargando modelo LogisticRegression para ComercialName...")
                                                try:
                                                    self.commercial_name_logistic_model = joblib.load(logistic_com_path)
                                                    print("Modelo cargado correctamente")
                                                except Exception as e:
                                                    print(f"Error al cargar modelo: {str(e)}")
                                    except Exception as e:
                                        print(f"Error al cargar embeddings: {str(e)}")
                                else:
                                    print(f"Error: Archivo de embeddings no encontrado en {checkpoint_embeddings_file}")
                            except Exception as e:
                                print(f"Error al cargar datos preprocesados: {str(e)}")
                        else:
                            print(f"Error: Archivo de datos no encontrado en {checkpoint_data_file}")
                    else:
                        print(f"Estado '{status}' no reconocido para reanudación, comenzando desde el principio")
                else:
                    print(f"El checkpoint existente corresponde a otro archivo, comenzando nuevo entrenamiento")
            except Exception as e:
                logger.error(f"Error al cargar checkpoint: {str(e)}")
                print(f"Error al cargar checkpoint: {str(e)}")
                print("Comenzando nuevo entrenamiento...")
        
        # Si no podemos reanudar, comenzar desde el principio
        if not resume_training:
            logger.info(f"Leyendo datos desde {file_path}")
            
            # Leer el archivo JSON
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Almacenar los datos
            self.data = data
            self.stats_data["total_items"] = len(data)
            
            # Preprocesar los datos
            logger.info("Preprocesando textos y generando embeddings...")
            
            # Crear listas para almacenar datos preprocesados
            texts = []
            categories = []
            commercial_names = []
            
            for item in tqdm(data, desc="Preprocesando datos"):
                vendor = item.get("Vendor", "")
                name = item.get("Name", "")
                category = item.get("Category", "")
                commercial_name = item.get("ComercialName", "")
                
                # Preprocesar texto
                processed_text = self._preprocess_text(vendor, name)
                
                texts.append(processed_text)
                categories.append(category)
                commercial_names.append(commercial_name)
            
            # Ajustar los label encoders
            logger.info("Ajustando encoders de etiquetas...")
            self.category_encoder = LabelEncoder()
            self.commercial_name_encoder = LabelEncoder()
            self.category_encoder.fit(categories)
            self.commercial_name_encoder.fit(commercial_names)
            
            # Generar embeddings
            logger.info("Generando embeddings...")
            print("\nGenerando embeddings para textos...")
            embeddings = []
            
            total_batches = (len(texts) + batch_size - 1) // batch_size
            progress_bar = tqdm(total=total_batches, desc="Generando embeddings", unit="batch", ncols=100)
            
            for i in range(0, len(texts), batch_size):
                # Guardar progreso periódicamente
                if i > 0 and i % (batch_size * 10) == 0:
                    print(f"\nGuardando checkpoint en {i}/{len(texts)} embeddings...")
                    # Guardar embeddings parciales
                    partial_embeddings = np.array(embeddings)
                    np.save(checkpoint_embeddings_file, partial_embeddings)
                    
                    # Guardar metadatos del checkpoint
                    checkpoint_meta = {
                        "file_path": os.path.normpath(file_path),  # Normalizar la ruta
                        "status": "embeddings_partial",
                        "processed": i,
                        "total": len(texts),
                        "timestamp": time.time()
                    }
                    with open(checkpoint_meta_file, 'w', encoding='utf-8') as f:
                        json.dump(checkpoint_meta, f, ensure_ascii=False, indent=2)
                    
                    print("Checkpoint guardado.")
                
                # Obtener el batch actual
                batch_texts = texts[i:i+batch_size]
                
                # Generar embeddings para el batch
                batch_embeddings = self.embedding_model.encode(batch_texts)
                embeddings.extend(batch_embeddings)
                
                # Actualizar barra de progreso
                progress_bar.update(1)
                progress_bar.set_postfix({"processed": min(i + batch_size, len(texts))})
            
            progress_bar.close()
            print(f"Completado: {len(embeddings)} embeddings generados")
            
            embeddings = np.array(embeddings)
            
            # Ajustar el vectorizador TF-IDF
            logger.info("Ajustando vectorizador TF-IDF...")
            self.tfidf_vectorizer = TfidfVectorizer(
                analyzer='word',
                ngram_range=(1, 3),
                max_features=5000,
                min_df=2
            )
            tfidf_features = self.tfidf_vectorizer.fit_transform(texts)
            
            # Guardar checkpoint de embeddings
            print("\nGuardando checkpoint de embeddings...")
            np.save(checkpoint_embeddings_file, embeddings)
            
            # Guardar datos procesados
            import pickle
            data_dict = {
                "data": self.data,
                "texts": texts,
                "categories": categories,
                "commercial_names": commercial_names,
                "category_encoder": self.category_encoder,
                "commercial_name_encoder": self.commercial_name_encoder,
                "tfidf_vectorizer": self.tfidf_vectorizer,
                "tfidf_features": tfidf_features
            }
            with open(checkpoint_data_file, 'wb') as f:
                pickle.dump(data_dict, f)
            
            # Actualizar metadatos del checkpoint
            checkpoint_meta = {
                "file_path": os.path.normpath(file_path),  # Normalizar la ruta para comparaciones correctas
                "status": "embeddings_generated",
                "total_items": len(self.data),
                "timestamp": time.time()
            }
            with open(checkpoint_meta_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_meta, f, ensure_ascii=False, indent=2)
            
            print("Checkpoint guardado.")
        
        # A partir de aquí, tenemos embeddings generados y continuamos con el entrenamiento
        # Transformar etiquetas a números
        y_category = self.category_encoder.transform(categories)
        y_commercial_name = self.commercial_name_encoder.transform(commercial_names)
        
        # Entrenar clasificadores de LogisticRegression
        logger.info("Entrenando clasificadores LogisticRegression...")
        
        # Modelo para Category
        print("Entrenando LogisticRegression para Category...")
        self.category_logistic_model = LogisticRegression(
            C=1.0,
            solver='saga',
            penalty='l2',
            max_iter=1000,
            n_jobs=-1,
            verbose=1,  # Muestra progreso en consola
            random_state=42
        )
        self.category_logistic_model.fit(embeddings, y_category)
        
        # Guardar checkpoint después de entrenar primer modelo
        joblib.dump(self.category_logistic_model, os.path.join(checkpoint_dir, "category_logistic_model.pkl"))
        checkpoint_meta["status"] = "category_model_trained"
        checkpoint_meta["timestamp"] = time.time()
        with open(checkpoint_meta_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_meta, f, ensure_ascii=False, indent=2)
        print("Checkpoint guardado después de entrenar modelo de categoría.")
        
        # Modelo para ComercialName
        print("Entrenando LogisticRegression para ComercialName...")
        self.commercial_name_logistic_model = LogisticRegression(
            C=1.0,
            solver='saga',
            penalty='l2',
            max_iter=1000,
            n_jobs=-1,
            verbose=1,  # Muestra progreso en consola
            random_state=42
        )
        self.commercial_name_logistic_model.fit(embeddings, y_commercial_name)
        
        # Guardar checkpoint después de entrenar segundo modelo
        joblib.dump(self.commercial_name_logistic_model, os.path.join(checkpoint_dir, "commercial_name_logistic_model.pkl"))
        checkpoint_meta["status"] = "commercial_name_model_trained"
        checkpoint_meta["timestamp"] = time.time()
        with open(checkpoint_meta_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_meta, f, ensure_ascii=False, indent=2)
        print("Checkpoint guardado después de entrenar modelo de nombre comercial.")
        
        # Entrenar modelos de LightGBM
        logger.info("Entrenando clasificadores LightGBM...")
        
        # Concatenar embeddings y TF-IDF para LightGBM
        logger.info("Convirtiendo TF-IDF a array para LightGBM...")
        tfidf_array = tfidf_features.toarray()
        
        # Concatenar características para LightGBM
        X_lgbm = np.hstack((embeddings, tfidf_array))
        
        # Parámetros para LightGBM (usando solo nomenclatura de scikit-learn)
        lgbm_params = {
            'objective': 'multiclass',
            'metric': 'multi_logloss',
            'boosting_type': 'gbdt',
            'learning_rate': 0.05,
            'num_leaves': 31,
            'min_child_samples': 20,
            'max_depth': -1,  # Sin límite
            'colsample_bytree': 0.9,
            'subsample': 0.8,
            'subsample_freq': 5,
            'verbose': 1,    # 1 para mostrar info, 0 silencioso, -1 para desactivar mensajes
            'n_jobs': -1  # En lugar de num_threads
        }
        
        # Callback para LightGBM con barra de progreso tqdm
        def lgbm_callback(env):
            # Verificar si es la primera iteración para inicializar la barra
            if env.iteration == 0:
                lgbm_callback.pbar = tqdm(total=env.end_iteration, desc="Iteraciones")
            
            # Actualizar la barra de progreso
            lgbm_callback.pbar.update(1)
            
            # Guardar checkpoint periódico cada 20 iteraciones
            if env.iteration > 0 and env.iteration % 20 == 0:
                # Guardar el checkpoint sin distinguir entre modelos
                checkpoint_meta["status"] = f"lgbm_partial_{env.iteration}"
                checkpoint_meta["timestamp"] = time.time()
                with open(checkpoint_meta_file, 'w', encoding='utf-8') as f:
                    json.dump(checkpoint_meta, f, ensure_ascii=False, indent=2)
            
            # Cerrar la barra cuando se completa
            if env.iteration == env.end_iteration - 1:
                lgbm_callback.pbar.close()
        
        # Modelo LightGBM para Category
        print("\nEntrenando LightGBM para Category...")
        lgbm_params['num_class'] = len(self.category_encoder.classes_)
        self.category_lgbm_model = lgb.LGBMClassifier(**lgbm_params)
        self.category_lgbm_model.fit(
            X_lgbm, 
            y_category, 
            callbacks=[lgbm_callback]
        )
        
        # Guardar checkpoint después de entrenar LightGBM para Category
        joblib.dump(self.category_lgbm_model, os.path.join(checkpoint_dir, "category_lgbm_model.pkl"))
        checkpoint_meta["status"] = "category_lgbm_trained"
        checkpoint_meta["timestamp"] = time.time()
        with open(checkpoint_meta_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_meta, f, ensure_ascii=False, indent=2)
        print("Checkpoint guardado después de entrenar LightGBM para categoría.")
        
        # Modelo LightGBM para ComercialName
        print("\nEntrenando LightGBM para ComercialName...")
        lgbm_params['num_class'] = len(self.commercial_name_encoder.classes_)
        self.commercial_name_lgbm_model = lgb.LGBMClassifier(**lgbm_params)
        self.commercial_name_lgbm_model.fit(
            X_lgbm, 
            y_commercial_name,
            callbacks=[lgbm_callback]
        )
        
        # Guardar checkpoint final
        joblib.dump(self.commercial_name_lgbm_model, os.path.join(checkpoint_dir, "commercial_name_lgbm_model.pkl"))
        checkpoint_meta["status"] = "completed"
        checkpoint_meta["timestamp"] = time.time()
        with open(checkpoint_meta_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_meta, f, ensure_ascii=False, indent=2)
        
        logger.info("Modelos entrenados exitosamente")
        print("\nEntrenamiento completado exitosamente. Todos los modelos entrenados.")
        
        # Guardar modelos
        self._save_models()
    
    def _save_models(self) -> None:
        """Guarda los modelos entrenados en el directorio de persistencia."""
        import joblib
        
        logger.info(f"Guardando modelos en {self.persist_directory}")
        
        # Guardar encoders
        joblib.dump(self.category_encoder, os.path.join(self.persist_directory, "category_encoder.pkl"))
        joblib.dump(self.commercial_name_encoder, os.path.join(self.persist_directory, "commercial_name_encoder.pkl"))
        
        # Guardar vectorizador TF-IDF
        joblib.dump(self.tfidf_vectorizer, os.path.join(self.persist_directory, "tfidf_vectorizer.pkl"))
        
        # Guardar modelos de LogisticRegression
        if self.category_logistic_model is not None:
            joblib.dump(self.category_logistic_model, os.path.join(self.persist_directory, "category_logistic_model.pkl"))
        
        if self.commercial_name_logistic_model is not None:
            joblib.dump(self.commercial_name_logistic_model, os.path.join(self.persist_directory, "commercial_name_logistic_model.pkl"))
        
        # Guardar modelos de LightGBM
        if self.category_lgbm_model is not None:
            joblib.dump(self.category_lgbm_model, os.path.join(self.persist_directory, "category_lgbm_model.pkl"))
        
        if self.commercial_name_lgbm_model is not None:
            joblib.dump(self.commercial_name_lgbm_model, os.path.join(self.persist_directory, "commercial_name_lgbm_model.pkl"))
        
        logger.info("Modelos guardados exitosamente")
    
    def _load_models(self) -> None:
        """Carga los modelos entrenados desde el directorio de persistencia."""
        import joblib
        
        logger.info(f"Cargando modelos desde {self.persist_directory}")
        
        # Cargar encoders
        if os.path.exists(os.path.join(self.persist_directory, "category_encoder.pkl")):
            self.category_encoder = joblib.load(os.path.join(self.persist_directory, "category_encoder.pkl"))
        
        if os.path.exists(os.path.join(self.persist_directory, "commercial_name_encoder.pkl")):
            self.commercial_name_encoder = joblib.load(os.path.join(self.persist_directory, "commercial_name_encoder.pkl"))
        
        # Cargar vectorizador TF-IDF
        if os.path.exists(os.path.join(self.persist_directory, "tfidf_vectorizer.pkl")):
            self.tfidf_vectorizer = joblib.load(os.path.join(self.persist_directory, "tfidf_vectorizer.pkl"))
        
        # Cargar modelos de LogisticRegression
        if os.path.exists(os.path.join(self.persist_directory, "category_logistic_model.pkl")):
            self.category_logistic_model = joblib.load(os.path.join(self.persist_directory, "category_logistic_model.pkl"))
        
        if os.path.exists(os.path.join(self.persist_directory, "commercial_name_logistic_model.pkl")):
            self.commercial_name_logistic_model = joblib.load(os.path.join(self.persist_directory, "commercial_name_logistic_model.pkl"))
        
        # Cargar modelos de LightGBM
        if os.path.exists(os.path.join(self.persist_directory, "category_lgbm_model.pkl")):
            self.category_lgbm_model = joblib.load(os.path.join(self.persist_directory, "category_lgbm_model.pkl"))
        
        if os.path.exists(os.path.join(self.persist_directory, "commercial_name_lgbm_model.pkl")):
            self.commercial_name_lgbm_model = joblib.load(os.path.join(self.persist_directory, "commercial_name_lgbm_model.pkl"))
        
        logger.info("Modelos cargados exitosamente")
    
    def _classify_with_llm(self, vendor: str, name: str) -> Tuple[str, str, float]:
        """
        Clasifica un elemento utilizando un LLM (OpenAI).
        
        Args:
            vendor: Nombre del proveedor
            name: Nombre del software
            
        Returns:
            Tuple con (categoría, nombre comercial, confianza)
        """
        if not self.use_llm:
            logger.warning("API key de OpenAI no configurada, no se puede usar LLM")
            return (None, None, 0.0)
        
        # Construir prompt con ejemplos de entrenamiento (few-shot learning)
        prompt = f"""Clasifica el siguiente software en categoría y nombre comercial.

Ejemplos:
1. Vendor: "McAfee, LLC", Name: "McAfee Agent" -> Category: "EDR", ComercialName: "McAfee"
2. Vendor: "Sophos Limited", Name: "Sophos Anti-Virus" -> Category: "Antivirus", ComercialName: "Sophos AV"
3. Vendor: "CrowdStrike, Inc.", Name: "CrowdStrike Windows Sensor" -> Category: "EDR", ComercialName: "CrowdStrike Falcon"
4. Vendor: "Qualys, Inc.", Name: "Qualys Cloud Security Agent" -> Category: "Vulnerability Management", ComercialName: "Qualys"

Software a clasificar:
Vendor: "{vendor}", Name: "{name}"

Responde con el formato siguiente exactamente:
Category: [CATEGORIA]
ComercialName: [NOMBRE_COMERCIAL]
"""
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "Eres un asistente experto en clasificación de software."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,
                max_tokens=50
            )
            
            result = response.choices[0].message.content.strip()
            
            # Extraer Category y ComercialName del resultado
            category_match = re.search(r"Category:\s*(.+)", result)
            commercial_name_match = re.search(r"ComercialName:\s*(.+)", result)
            
            category = category_match.group(1) if category_match else None
            commercial_name = commercial_name_match.group(1) if commercial_name_match else None
            
            # La confianza para LLM la establecemos arbitrariamente en 0.9
            return (category, commercial_name, 0.9)
            
        except Exception as e:
            logger.error(f"Error al clasificar con LLM: {str(e)}")
            return (None, None, 0.0)
    
    def _classify_item(self, item: Dict[str, str]) -> Dict[str, Any]:
        """
        Clasifica un elemento utilizando el pipeline de clasificación.
        
        Args:
            item: Diccionario con Vendor y Name
            
        Returns:
            Diccionario con la clasificación
        """
        vendor = item.get("Vendor", "")
        name = item.get("Name", "")
        
        # Preprocesar texto
        processed_text = self._preprocess_text(vendor, name)
        
        # Obtener embedding
        embedding = self._get_embedding(processed_text)
        embedding = embedding.reshape(1, -1)  # Reshape para predicción
        
        # Clasificador primario: LogisticRegression
        category_probs = self.category_logistic_model.predict_proba(embedding)[0]
        commercial_name_probs = self.commercial_name_logistic_model.predict_proba(embedding)[0]
        
        category_idx = np.argmax(category_probs)
        commercial_name_idx = np.argmax(commercial_name_probs)
        
        category_prob = category_probs[category_idx]
        commercial_name_prob = commercial_name_probs[commercial_name_idx]
        
        category = self.category_encoder.inverse_transform([category_idx])[0]
        commercial_name = self.commercial_name_encoder.inverse_transform([commercial_name_idx])[0]
        
        # Si la probabilidad es menor que el umbral, pasar al clasificador secundario
        # Caso 1: Solo la categoría necesita mejora
        if category_prob < self.primary_threshold and commercial_name_prob >= self.primary_threshold:
            logger.info(f"Solo Category necesita mejora ({category_prob:.4f}), usando LightGBM")
            
            # Generar características TF-IDF
            tfidf_features = self.tfidf_vectorizer.transform([processed_text]).toarray()
            
            # Concatenar con embedding
            X_lgbm = np.hstack((embedding, tfidf_features))
            
            # Clasificar SOLO Category con LightGBM
            category_probs_lgbm = self.category_lgbm_model.predict_proba(X_lgbm)[0]
            category_idx_lgbm = np.argmax(category_probs_lgbm)
            category_prob_lgbm = category_probs_lgbm[category_idx_lgbm]
            category_lgbm = self.category_encoder.inverse_transform([category_idx_lgbm])[0]
            
            # Actualizar sólo Category si LightGBM es mejor
            if category_prob_lgbm > category_prob:
                category = category_lgbm
                category_prob = category_prob_lgbm
        
        # Caso 2: Solo el nombre comercial necesita mejora
        elif category_prob >= self.primary_threshold and commercial_name_prob < self.primary_threshold:
            logger.info(f"Solo ComercialName necesita mejora ({commercial_name_prob:.4f}), usando LightGBM")
            
            # Generar características TF-IDF
            tfidf_features = self.tfidf_vectorizer.transform([processed_text]).toarray()
            
            # Concatenar con embedding
            X_lgbm = np.hstack((embedding, tfidf_features))
            
            # Clasificar SOLO ComercialName con LightGBM
            commercial_name_probs_lgbm = self.commercial_name_lgbm_model.predict_proba(X_lgbm)[0]
            commercial_name_idx_lgbm = np.argmax(commercial_name_probs_lgbm)
            commercial_name_prob_lgbm = commercial_name_probs_lgbm[commercial_name_idx_lgbm]
            commercial_name_lgbm = self.commercial_name_encoder.inverse_transform([commercial_name_idx_lgbm])[0]
            
            # Actualizar sólo ComercialName si LightGBM es mejor
            if commercial_name_prob_lgbm > commercial_name_prob:
                commercial_name = commercial_name_lgbm
                commercial_name_prob = commercial_name_prob_lgbm
        
        # Caso 3: Ambos necesitan mejora
        elif category_prob < self.primary_threshold or commercial_name_prob < self.primary_threshold:
            logger.info(f"Probabilidad LogisticRegression baja ({category_prob:.4f}, {commercial_name_prob:.4f}), usando LightGBM")
            
            # Generar características TF-IDF
            tfidf_features = self.tfidf_vectorizer.transform([processed_text]).toarray()
            
            # Concatenar con embedding
            X_lgbm = np.hstack((embedding, tfidf_features))
            
            # Clasificar con LightGBM
            category_probs_lgbm = self.category_lgbm_model.predict_proba(X_lgbm)[0]
            commercial_name_probs_lgbm = self.commercial_name_lgbm_model.predict_proba(X_lgbm)[0]
            
            category_idx_lgbm = np.argmax(category_probs_lgbm)
            commercial_name_idx_lgbm = np.argmax(commercial_name_probs_lgbm)
            
            category_prob_lgbm = category_probs_lgbm[category_idx_lgbm]
            commercial_name_prob_lgbm = commercial_name_probs_lgbm[commercial_name_idx_lgbm]
            
            category_lgbm = self.category_encoder.inverse_transform([category_idx_lgbm])[0]
            commercial_name_lgbm = self.commercial_name_encoder.inverse_transform([commercial_name_idx_lgbm])[0]
            
            # Actualizar valores si LightGBM es mejor
            if category_prob_lgbm > category_prob:
                category = category_lgbm
                category_prob = category_prob_lgbm
            
            if commercial_name_prob_lgbm > commercial_name_prob:
                commercial_name = commercial_name_lgbm
                commercial_name_prob = commercial_name_prob_lgbm
            
            # Si aún no es suficiente, usar LLM
            if (category_prob < self.secondary_threshold or commercial_name_prob < self.secondary_threshold) and self.use_llm:
                logger.info(f"Probabilidad LightGBM baja ({category_prob:.4f}, {commercial_name_prob:.4f}), usando LLM")
                
                llm_category, llm_commercial_name, llm_confidence = self._classify_with_llm(vendor, name)
                
                if llm_category and llm_category not in self.category_encoder.classes_:
                    logger.warning(f"LLM generó una categoría desconocida: {llm_category}")
                else:
                    category = llm_category if llm_category else category
                
                if llm_commercial_name and llm_commercial_name not in self.commercial_name_encoder.classes_:
                    logger.warning(f"LLM generó un nombre comercial desconocido: {llm_commercial_name}")
                else:
                    commercial_name = llm_commercial_name if llm_commercial_name else commercial_name
                
                # Actualizar confianza solo para propósitos de registro
                if llm_category:
                    category_prob = llm_confidence
                
                if llm_commercial_name:
                    commercial_name_prob = llm_confidence
        
        # Construir el resultado
        result = item.copy()
        result.update({
            "Category": category,
            "ComercialName": commercial_name,
            "Confidence": min(category_prob, commercial_name_prob)
        })
        
        return result
    
    def classify_from_file(
        self, 
        file_path: str, 
        output_path: Optional[str] = None,
        threshold: float = 0.0,
        batch_save_size: int = 10000  # Tamaño del lote para guardar resultados parciales
    ) -> List[Dict[str, Any]]:
        """
        Clasifica elementos desde un archivo JSON.
        
        Args:
            file_path: Ruta al archivo JSON con elementos a clasificar
            output_path: Ruta para guardar los resultados (opcional)
            threshold: Umbral mínimo de confianza para incluir resultados
            batch_save_size: Tamaño del lote para guardar resultados parciales
            
        Returns:
            Lista de diccionarios con elementos clasificados
        """
        if self.category_logistic_model is None or self.commercial_name_logistic_model is None:
            try:
                print("Cargando modelos previamente entrenados...")
                self._load_models()
                print("Modelos cargados correctamente")
            except Exception as e:
                logger.error(f"No se pueden cargar los modelos: {str(e)}")
                raise ValueError("Es necesario entrenar los modelos primero con ingest_from_file()")
        
        logger.info(f"Leyendo datos para clasificar desde {file_path}")
        print(f"\nLeyendo datos para clasificar desde {file_path}...")
        
        # Leer el archivo JSON
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        total_items = len(data)
        logger.info(f"Clasificando {total_items} elementos...")
        print(f"Archivo leido: {total_items} elementos para clasificar")
        
        # Inicializar contadores para estadísticas
        confidence_levels = {
            "alto": 0,   # >= 0.8
            "medio": 0,  # 0.6-0.8
            "bajo": 0    # < 0.6
        }
        
        # Clasificar elementos con barra de progreso detallada
        print(f"\nClasificando {total_items} elementos...")
        results = []
        progress_bar = tqdm(total=total_items, desc="Clasificando", unit="item", ncols=100)
        
        # Directorio para resultados parciales
        if output_path:
            output_dir = os.path.dirname(output_path) or "."
            os.makedirs(output_dir, exist_ok=True)
        
        for i, item in enumerate(data):
            # Clasificar el elemento
            result = self._classify_item(item)
            confidence = result.get("Confidence", 0)
            
            # Actualizar contadores de confianza
            if confidence >= 0.8:
                confidence_levels["alto"] += 1
            elif confidence >= 0.6:
                confidence_levels["medio"] += 1
            else:
                confidence_levels["bajo"] += 1
                
            # Solo incluir si supera el umbral de confianza
            if confidence >= threshold:
                results.append(result)
            
            # Actualizar barra de progreso
            progress_bar.update(1)
            progress_bar.set_postfix({
                "alto": f"{confidence_levels['alto']/max(1, i+1):.1%}", 
                "medio": f"{confidence_levels['medio']/max(1, i+1):.1%}", 
                "bajo": f"{confidence_levels['bajo']/max(1, i+1):.1%}"
            })
            
            # Guardar resultados parciales periódicamente
            if output_path and (i+1) % batch_save_size == 0:
                batch_num = (i+1) // batch_save_size
                temp_output = f"{os.path.splitext(output_path)[0]}_batch_{batch_num}{os.path.splitext(output_path)[1]}"
                print(f"\nGuardando lote {batch_num} ({i+1} de {total_items} elementos) en {temp_output}...")
                
                # Serializar resultados para JSON
                serializable_results = [serialize_for_json(r) for r in results]
                
                try:
                    with open(temp_output, 'w', encoding='utf-8') as f:
                        json.dump(serializable_results, f, ensure_ascii=False, indent=2)
                    print(f"Lote {batch_num} guardado exitosamente")
                except Exception as e:
                    print(f"Error al guardar lote {batch_num}: {str(e)}")
        
        progress_bar.close()
        
        # Mostrar estadisticas finales
        print(f"\nClasificacion completada: {len(results)} elementos clasificados")
        print(f"  * Confianza alta (>= 0.8): {confidence_levels['alto']} ({confidence_levels['alto']/total_items:.1%})")
        print(f"  * Confianza media (0.6-0.8): {confidence_levels['medio']} ({confidence_levels['medio']/total_items:.1%})")
        print(f"  * Confianza baja (< 0.6): {confidence_levels['bajo']} ({confidence_levels['bajo']/total_items:.1%})")
        
        # Guardar resultados si se especifica una ruta
        if output_path:
            logger.info(f"Guardando resultados en {output_path}")
            print(f"\nGuardando resultados finales en {output_path}...")
            
            # Serializar resultados para JSON
            serializable_results = [serialize_for_json(r) for r in results]
            
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(serializable_results, f, ensure_ascii=False, indent=2)
                print(f"Resultados guardados exitosamente")
            except Exception as e:
                logger.error(f"Error al guardar resultados: {str(e)}")
                print(f"Error al guardar resultados: {str(e)}")
                
                # Intentar guardar en un formato alternativo
                fallback_output = f"{os.path.splitext(output_path)[0]}_fallback.json"
                print(f"Intentando guardar en formato alternativo: {fallback_output}")
                try:
                    with open(fallback_output, 'w', encoding='utf-8') as f:
                        json.dump(serializable_results, f, ensure_ascii=False, indent=1)
                    print(f"Resultados guardados en {fallback_output}")
                except Exception as e2:
                    print(f"Error al guardar en formato alternativo: {str(e2)}")
        
        return results
    
    def stats(self) -> Dict[str, Any]:
        """
        Devuelve estadísticas sobre el clasificador.
        
        Returns:
            Diccionario con estadísticas
        """
        return self.stats_data
