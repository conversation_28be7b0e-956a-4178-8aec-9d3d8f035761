#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import json
import time
import pandas as pd
from classifier import SoftwareClassifier

def main():
    parser = argparse.ArgumentParser(description='Software Classifier Pipeline')
    parser.add_argument('--train', type=str, help='Archivo JSON para entrenamiento', default='groundtruth.json')
    parser.add_argument('--test', type=str, help='Archivo JSON para clasificación', default='inventory_clean_sample.json')
    parser.add_argument('--output', type=str, help='Ruta para guardar resultados', default='classified_results.json')
    parser.add_argument('--model', type=str, help='Modelo de embeddings', default='all-MiniLM-L6-v2')
    parser.add_argument('--primary-threshold', type=float, help='Umbral para clasificador primario', default=0.8)
    parser.add_argument('--secondary-threshold', type=float, help='Umbral para clasificador secundario', default=0.6)
    parser.add_argument('--openai-api-key', type=str, help='API key para OpenAI (opcional)', default=None)
    parser.add_argument('--evaluate', action='store_true', help='Evaluar rendimiento del modelo')
    parser.add_argument('--skip-train', action='store_true', help='Omitir entrenamiento y usar modelos guardados')
    parser.add_argument('--no-resume', action='store_true', help='No reanudar entrenamiento, comenzar desde cero')
    parser.add_argument('--debug-resume', action='store_true', help='Mostrar información adicional de depuración para reanudación')
    parser.add_argument('--batch-save-size', type=int, default=10000, help='Tamaño del lote para guardar resultados parciales')
    
    args = parser.parse_args()
    
    start_time = time.time()
    
    print("\n===== INICIALIZACIÓN DEL CLASIFICADOR =====")
    # Inicializar el clasificador
    classifier = SoftwareClassifier(
        collection_name="software_inventory",
        persist_directory="./models",
        embedding_model=args.model,
        primary_threshold=args.primary_threshold,
        secondary_threshold=args.secondary_threshold,
        openai_api_key=args.openai_api_key
    )
    
    if not args.skip_train:
        # Ingesta inicial desde groundtruth
        print(f"\n===== INGESTA INICIAL DESDE {args.train} =====")
        if args.debug_resume:
            print(f"Parámetros:\n- Archivo: {args.train}\n- Resume: {not args.no_resume}\n- Directorio de checkpoints: ./models/checkpoints")
        
        classifier.ingest_from_file(args.train, batch_size=50, resume=not args.no_resume)
    
    # Verificar estadísticas
    print("\n===== ESTADÍSTICAS =====")
    stats = classifier.stats()
    print(f"• Colección: {stats['collection_name']}")
    print(f"• Modelo: {stats['embedding_model']}")
    print(f"• Total de elementos: {stats['total_items']}")
    
    # Clasificar elementos con un umbral de confianza
    print(f"\n===== CLASIFICACIÓN DE {args.test} =====")
    results = classifier.classify_from_file(
        args.test, 
        output_path=args.output,
        threshold=0.0,  # No filtrar por umbral, para ver todos los resultados
        batch_save_size=args.batch_save_size
    )
    
    # Mostrar algunos ejemplos de resultados
    print("\n===== EJEMPLOS DE RESULTADOS =====")
    for i, result in enumerate(results[:5]):  # Mostrar solo los primeros 5
        print(f"Item {i+1}:")
        print(f"  Vendor: {result['Vendor']}")
        print(f"  Name: {result['Name']}")
        print(f"  Categoría asignada: {result['Category']}")
        print(f"  Nombre comercial: {result['ComercialName']}")
        if 'Confidence' in result:
            print(f"  Confianza: {result['Confidence']:.4f}")
    
    # Evaluar modelo si se solicita
    if args.evaluate and not args.skip_train:
        print("\n===== EVALUACIÓN DEL MODELO =====")
        
        # Leer los datos de groundtruth
        with open(args.train, 'r', encoding='utf-8') as f:
            truth_data = json.load(f)
        
        # Seleccionar un subconjunto para evaluación (20%)
        import random
        random.seed(42)
        eval_data = random.sample(truth_data, k=min(100, int(len(truth_data) * 0.2)))
        
        # Clasificar el conjunto de evaluación
        eval_results = []
        for item in eval_data:
            # Hacemos una copia del item sin Category y ComercialName
            test_item = {
                'Vendor': item.get('Vendor', ''),
                'Name': item.get('Name', '')
            }
            # Clasificamos
            result = classifier._classify_item(test_item)
            # Guardamos resultado y valor verdadero
            eval_results.append({
                'Vendor': item.get('Vendor', ''),
                'Name': item.get('Name', ''),
                'True_Category': item.get('Category', ''),
                'Pred_Category': result.get('Category', ''),
                'True_ComercialName': item.get('ComercialName', ''),
                'Pred_ComercialName': result.get('ComercialName', ''),
                'Confidence': result.get('Confidence', 0)
            })
        
        # Convertir a DataFrame para análisis
        df = pd.DataFrame(eval_results)
        
        # Calcular métricas
        category_accuracy = (df['True_Category'] == df['Pred_Category']).mean()
        commercial_name_accuracy = (df['True_ComercialName'] == df['Pred_ComercialName']).mean()
        overall_accuracy = ((df['True_Category'] == df['Pred_Category']) & 
                           (df['True_ComercialName'] == df['Pred_ComercialName'])).mean()
        
        print(f"Precisión en Categoría: {category_accuracy:.4f}")
        print(f"Precisión en Nombre Comercial: {commercial_name_accuracy:.4f}")
        print(f"Precisión global: {overall_accuracy:.4f}")
        
        # Analizar por nivel de confianza
        confidence_bins = [0, 0.6, 0.8, 1.0]
        bin_labels = ['<0.6', '0.6-0.8', '>0.8']
        df['Confidence_Bin'] = pd.cut(df['Confidence'], bins=confidence_bins, labels=bin_labels)
        
        print("\nPrecisión por nivel de confianza:")
        for bin_label in bin_labels:
            bin_df = df[df['Confidence_Bin'] == bin_label]
            if len(bin_df) > 0:
                bin_accuracy = ((bin_df['True_Category'] == bin_df['Pred_Category']) & 
                               (bin_df['True_ComercialName'] == bin_df['Pred_ComercialName'])).mean()
                print(f"  • Confianza {bin_label}: {bin_accuracy:.4f} ({len(bin_df)} elementos)")
    
    # Calcular tiempo total
    end_time = time.time()
    elapsed_time = end_time - start_time
    minutes = int(elapsed_time // 60)
    seconds = int(elapsed_time % 60)
    
    print(f"\n===== PROCESO COMPLETADO EN {minutes}m {seconds}s =====")
    print(f"Rendimiento:")
    print(f"  * Se clasificaron {len(results)} elementos")
    print(f"  * Tiempo promedio por elemento: {elapsed_time*1000/len(results):.2f} ms")
    print(f"  * Resultados guardados en '{args.output}'")
    
    # Mensaje de cierre
    print("\nTip: Puedes analizar los modelos usando 'python analyze_models.py --results {args.output}'")
    print("    Esto te mostrara informacion detallada sobre la distribucion de confianza y caracteristicas principales.")

if __name__ == "__main__":
    try:
        print("\n" + "="*50)
        print("   PIPELINE DE CLASIFICACION DE SOFTWARE")
        print("="*50)
        print("\nVersion: 1.1 (con barras de progreso)")
        main()
    except KeyboardInterrupt:
        print("\n\nProceso interrumpido por el usuario")
    except Exception as e:
        print(f"\n\nError: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n" + "="*50 + "\n")
