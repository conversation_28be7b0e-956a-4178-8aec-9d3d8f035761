import { NextRequest, NextResponse } from 'next/server';
import { listFilesInS3 } from '@/lib/s3-utils';
import { requireAuth } from '@/lib/auth-utils';

export async function GET(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request);
  if (authError) {
    return authError;
  }
  try {
    try {
      // List all JSON files in the tenants directory from S3
      const files = await listFilesInS3('tenants/');

      // Extract tenant information from S3 file paths
      const tenants = files
        .filter(file => file.startsWith('tenants/') && file.endsWith('.json'))
        .map(file => {
          // Extract filename from full S3 path (e.g., "tenants/example.json" -> "example")
          const fileName = file.split('/').pop() || '';
          const tenantId = fileName.replace('.json', '');
          
          // Remove "_export" from the name if it exists and clean the name
          const cleanName = tenantId
            .replace(/_export$/, '')
            .replace(/_/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase()); // Capitalize first letter of each word

          return {
            id: tenantId,
            name: cleanName,
          };
        })
        .sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically

      return NextResponse.json(tenants);
    } catch (s3Error) {
      console.error('Error reading tenants from S3:', s3Error);
      return NextResponse.json(
        { error: 'Failed to read tenants from S3' },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error('Error fetching tenants:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tenants' },
      { status: 500 }
    );
  }
}
