// Utility functions for loading and saving data from JSON files

// Load categories from JSON file
export async function loadCategories(): Promise<string[]> {
  try {
    // In a real application, this would fetch from your API or file system
    // For this example, we'll use a mock list

    // Simulating API call
    // const response = await fetch('/api/data/categories');
    // const data = await response.json();
    // return data.categories;

    // Mock data
    return [
      'Development Tools',
      'EDR',
      'Ofimatica',
      'Security Software',
      'Multimedia',
      'Productivity',
      'Communication',
      'Database',
      'Virtualization',
      'Operating System',
      'Browser',
      'Antivirus',
      'Firewall',
      'VPN',
      'Remote Access',
    ];
  } catch (error) {
    console.error('Error loading categories:', error);
    return [];
  }
}

// Load commercial names from JSON files (both original and user additions)
export async function loadCommercialNames(): Promise<string[]> {
  try {
    // In a real application, this would fetch from your API or file system
    // For this example, we'll use a mock list

    // Simulating API calls
    // const response1 = await fetch('/api/data/commercial-names');
    // const data1 = await response1.json();

    // const response2 = await fetch('/api/data/commercial-names-additions');
    // const data2 = await response2.json();

    // Combine both lists and remove duplicates
    // const combinedList = [...data1.commercialNames, ...data2.commercialNames];
    // return Array.from(new Set(combinedList)).sort();

    // Mock data
    const originalList = [
      'Windows',
      'Cortex XDR',
      'Microsoft Office',
      'Qualys Security',
      'VLC Media Player',
      'Adobe Acrobat',
      'Google Chrome',
      'Mozilla Firefox',
      'Zoom',
      'Microsoft Teams',
      'Slack',
      'Visual Studio Code',
      'IntelliJ IDEA',
      'Oracle Database',
      'MySQL',
    ];

    // Get user additions from localStorage if available (in a real app, this would come from the JSON file)
    let userAdditions: string[] = [];
    if (typeof window !== 'undefined') {
      const storedAdditions = localStorage.getItem('commercialNameAdditions');
      if (storedAdditions) {
        userAdditions = JSON.parse(storedAdditions);
      }
    }

    // Combine both lists and remove duplicates
    const combinedList = [...originalList, ...userAdditions];
    return Array.from(new Set(combinedList)).sort();
  } catch (error) {
    console.error('Error loading commercial names:', error);
    return [];
  }
}

// Save a new commercial name to the additions file
export async function saveNewCommercialName(newName: string): Promise<void> {
  try {
    // In a real application, this would save to your API or file system
    // For this example, we'll use localStorage to simulate persistence

    // Simulating API call
    // await fetch('/api/data/commercial-names-additions', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify({ name: newName }),
    // });

    // Mock implementation using localStorage
    if (typeof window !== 'undefined') {
      const storedAdditions = localStorage.getItem('commercialNameAdditions');
      const additions: string[] = storedAdditions
        ? JSON.parse(storedAdditions)
        : [];

      if (!additions.includes(newName)) {
        additions.push(newName);
        localStorage.setItem(
          'commercialNameAdditions',
          JSON.stringify(additions)
        );
      }
    }
  } catch (error) {
    console.error('Error saving new commercial name:', error);
    throw error;
  }
}

// API route implementation for a real application
// This would be in app/api/data/commercial-names-additions/route.ts
/*
import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function GET() {
  try {
    const filePath = path.join(process.cwd(), 'data', 'commercial_names_list_additions.json');
    const fileData = await fs.readFile(filePath, 'utf8');
    const data = JSON.parse(fileData);
    
    return NextResponse.json({ commercialNames: data.commercialNames || [] });
  } catch (error) {
    console.error('Error reading commercial names additions:', error);
    return NextResponse.json({ commercialNames: [] });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name } = await request.json();
    
    if (!name || typeof name !== 'string') {
      return NextResponse.json({ error: 'Invalid commercial name' }, { status: 400 });
    }
    
    const filePath = path.join(process.cwd(), 'data', 'commercial_names_list_additions.json');
    
    // Read existing data
    let data;
    try {
      const fileData = await fs.readFile(filePath, 'utf8');
      data = JSON.parse(fileData);
    } catch (error) {
      // File doesn't exist or is invalid, create new data structure
      data = { commercialNames: [] };
    }
    
    // Add new name if it doesn't already exist
    if (!data.commercialNames.includes(name)) {
      data.commercialNames.push(name);
      data.commercialNames.sort();
      
      // Write back to file
      await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error saving commercial name:', error);
    return NextResponse.json({ error: 'Failed to save commercial name' }, { status: 500 });
  }
}
*/
