import {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
  ListObjectsV2Command,
} from '@aws-sdk/client-s3';

// Configuración del cliente S3
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET || 'batuta-ai';

/**
 * Lee un archivo JSON desde S3
 */
export async function readFromS3(filePath: string): Promise<any> {
  try {
    const fullPath = filePath;
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: fullPath,
    });

    const response = await s3Client.send(command);

    if (!response.Body) {
      throw new Error(`No data found for file: ${filePath}`);
    }

    const data = await response.Body.transformToString();
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading from S3 - ${filePath}:`, error);
    throw error;
  }
}

/**
 * Escribe un archivo JSON a S3
 */
export async function writeToS3(filePath: string, data: any): Promise<void> {
  try {
    const fullPath = filePath;
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: fullPath,
      Body: JSON.stringify(data, null, 2),
      ContentType: 'application/json',
    });

    await s3Client.send(command);
    console.log(`Successfully wrote to S3: ${filePath}`);
  } catch (error) {
    console.error(`Error writing to S3 - ${filePath}:`, error);
    throw error;
  }
}

/**
 * Lista archivos en un directorio de S3
 */
export async function listFilesInS3(prefix: string): Promise<string[]> {
  try {
    const fullPrefix = prefix;
    const command = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: fullPrefix,
    });

    const response = await s3Client.send(command);

    if (!response.Contents) {
      return [];
    }

    return response.Contents.map(obj => {
      let key = obj.Key || '';
      return key;
    }).filter(key => key.endsWith('.json'));
  } catch (error) {
    console.error(`Error listing files from S3 - ${prefix}:`, error);
    throw error;
  }
}

/**
 * Verifica si un archivo existe en S3
 */
export async function existsInS3(filePath: string): Promise<boolean> {
  try {
    const fullPath = filePath;
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: fullPath,
    });

    await s3Client.send(command);
    return true;
  } catch (error: any) {
    if (error.name === 'NoSuchKey') {
      return false;
    }
    throw error;
  }
}

/**
 * Lee archivos de tenants específicos desde S3
 */
export async function readTenantDataFromS3(tenantId: string): Promise<any> {
  const filePath = `tenants/${tenantId}.json`;
  return await readFromS3(filePath);
}

/**
 * Lee datos de review desde S3
 */
export async function readReviewDataFromS3(
  type: 'correct' | 'edited'
): Promise<any[]> {
  const filePath = `review/inventory_${type}.json`;
  try {
    return await readFromS3(filePath);
  } catch (error: any) {
    if (error.name === 'NoSuchKey') {
      return [];
    }
    throw error;
  }
}

/**
 * Escribe datos de review a S3
 */
export async function writeReviewDataToS3(
  type: 'correct' | 'edited',
  data: any[]
): Promise<void> {
  const filePath = `review/inventory_${type}.json`;
  await writeToS3(filePath, data);
}

/**
 * Lee commercial names additions desde S3
 */
export async function readCommercialNamesAdditionsFromS3(): Promise<{
  commercialNames: string[];
}> {
  try {
    return await readFromS3('commercial_names_list_additions.json');
  } catch (error: any) {
    if (error.name === 'NoSuchKey') {
      return { commercialNames: [] };
    }
    throw error;
  }
}

/**
 * Escribe commercial names additions a S3
 */
export async function writeCommercialNamesAdditionsToS3(data: {
  commercialNames: string[];
}): Promise<void> {
  await writeToS3('commercial_names_list_additions.json', data);
}
