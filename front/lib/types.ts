export interface SoftwareItem {
  id: string;
  name: string;
  provider: string;
  commercialName?: string;
  category?: string;
  feedback?: 'positive' | 'negative' | 'corrected' | null;
}

export interface EditingItem {
  id: string;
  commercialName: string;
  category: string;
}

export interface Tenant {
  id: string;
  name: string;
}

export interface FeedbackData {
  itemId: string;
  feedbackType: 'positive' | 'negative' | 'corrected' | null;
  tenantId: string;
  timestamp: string;
  action?: 'correction' | 'feedback';
  commercialName?: string;
  category?: string;
}

export interface CorrectionData extends FeedbackData {
  action: 'correction';
  commercialName: string;
  category: string;
  originalCommercialName?: string;
  originalCategory?: string;
}
