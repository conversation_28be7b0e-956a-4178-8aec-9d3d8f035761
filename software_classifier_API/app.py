from datetime import datetime
from flask import Flask, request, jsonify
from flask_restx import Api, Resource, fields
from classifier import SoftwareClassifier

# Configuración de Flask y Swagger
app = Flask(__name__)
api = Api(app, 
          version='1.0', 
          title='Product Classifier API',
          description='API para clasificación de software usando múltiples modelos ML',
          doc='/swagger/')

# Namespace para las rutas
ns = api.namespace('classifier', description='Clasificacion de software')

# Modelos para Swagger
prediction_input = api.model('PredictionInput', {
    'name': fields.String(required=True, description='Nombre del producto', example='CrowdStrike Windows Sensor'),
    'vendor': fields.String(required=True, description='Proveedor', example='CrowdStrike, Inc.')
})

# Modelos para predicción en lote
batch_item = api.model('BatchItem', {
    'name': fields.String(required=True, description='Nombre del producto'),
    'vendor': fields.String(required=True, description='Proveedor')
})

batch_input = api.model('BatchInput', {
    'items': fields.List(fields.Nested(batch_item), required=True, description='Lista de productos a clasificar')
})

batch_output_item = api.model('BatchOutputItem', {
    'category': fields.String(description='Categoría'),
    'comercial_name': fields.String(description='Nombre comercial')
})

batch_output = api.model('BatchOutput', {
    'predictions': fields.List(fields.Nested(batch_output_item), description='Lista de predicciones')
})

prediction_output = api.model('PredictionOutput', {
    'category': fields.String(description='Categoría'),
    'comercial_name': fields.String(description='Nombre comercial')
})

category_output = api.model('CategoryOutput', {
    'category': fields.String(description='Categoría')
})

comercial_output = api.model('ComercialOutput', {
    'comercial_name': fields.String(description='Nombre comercial')
})

# Inicializar el clasificador de software (ajusta la ruta según tu configuración)
MODELS_PATH = 'models'
software_classifier = None

def initialize_classifier():
    """Inicializa el clasificador al arrancar la aplicación"""
    global software_classifier
    try:
        software_classifier = SoftwareClassifier(MODELS_PATH)
        print("🚀 API lista para recibir peticiones!")
    except Exception as e:
        print(f"❌ Error inicializando clasificador: {str(e)}")

@ns.route('/predict')
class PredictBoth(Resource):
    @ns.expect(prediction_input)
    @ns.marshal_with(prediction_output)
    def post(self):
        """Predice tanto categoría como nombre comercial"""
        try:
            data = request.json
            name = data.get('name', '')
            vendor = data.get('vendor', '')
            
            if not name or not vendor:
                return {'error': 'Se requieren tanto name como vendor'}, 400
            
            predictions = software_classifier.predict_both(name, vendor)
            
            return {
                'category': predictions['category'],
                'comercial_name': predictions['comercial_name']
            }
            
        except Exception as e:
            return {'error': f'Error en predicción: {str(e)}'}, 500

@ns.route('/predict/batch')
class PredictBatch(Resource):
    @ns.expect(batch_input)
    @ns.marshal_with(batch_output)
    def post(self):
        """Predice categoría y nombre comercial para una lista de productos"""
        try:
            data = request.json
            items = data.get('items', [])
            
            if not items:
                return {'error': 'Se requiere una lista de items no vacía'}, 400
            
            predictions = []
            for item in items:
                name = item.get('name', '')
                vendor = item.get('vendor', '')
                
                if not name or not vendor:
                    # Si falta algún campo, agregar un resultado de error
                    predictions.append({
                        'category': 'Error: Faltan campos',
                        'comercial_name': 'Error: Faltan campos'
                    })
                else:
                    # Realizar predicción
                    result = software_classifier.predict_both(name, vendor)
                    predictions.append({
                        'category': result['category'],
                        'comercial_name': result['comercial_name']
                    })
            
            return {
                'predictions': predictions
            }
            
        except Exception as e:
            return {'error': f'Error en predicción batch: {str(e)}'}, 500

@ns.route('/predict/category')
class PredictCategory(Resource):
    @ns.expect(prediction_input)
    @ns.marshal_with(category_output)
    def post(self):
        """Predice solo la categoría"""
        try:
            data = request.json
            name = data.get('name', '')
            vendor = data.get('vendor', '')
            
            if not name or not vendor:
                return {'error': 'Se requieren tanto name como vendor'}, 400
            
            category = software_classifier.predict_category(name, vendor)
            
            return {
                'category': category,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'error': f'Error en predicción: {str(e)}'}, 500

@ns.route('/predict/comercial')
class PredictComercial(Resource):
    @ns.expect(prediction_input)
    @ns.marshal_with(comercial_output)
    def post(self):
        """Predice solo el nombre comercial"""
        try:
            data = request.json
            name = data.get('name', '')
            vendor = data.get('vendor', '')
            
            if not name or not vendor:
                return {'error': 'Se requieren tanto name como vendor'}, 400
            
            comercial_name = software_classifier.predict_comercial_name(name, vendor)
            
            return {
                'comercial_name': comercial_name,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'error': f'Error en predicción: {str(e)}'}, 500

@ns.route('/health')
class Health(Resource):
    def get(self):
        """Endpoint de salud de la API"""
        return {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'classifier_ready': software_classifier is not None and software_classifier.is_ready()
        }

@ns.route('/metadata')
class Metadata(Resource):
    def get(self):
        """Obtiene la metadata de los modelos"""
        try:
            if software_classifier is None:
                return {'error': 'Clasificador no inicializado'}, 500
            
            metadata = software_classifier.get_metadata()
            return {
                'metadata': metadata,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'error': f'Error obteniendo metadata: {str(e)}'}, 500


if __name__ == '__main__':
    # Inicializar clasificador
    initialize_classifier()
    
    # Ejecutar la API
    app.run(debug=True, host='0.0.0.0', port=5000)
